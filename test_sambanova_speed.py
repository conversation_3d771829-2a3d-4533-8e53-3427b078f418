#!/usr/bin/env python3
"""
SambaNova API 速度测试脚本
测试原始端点和代理端点的响应速度差距
"""

import httpx
import time
import json
import asyncio
from typing import Dict, List
import statistics

class SambaNovaSpeedTester:
    def __init__(self):
        # API端点配置
        self.endpoints = {
            "原始端点": "https://api.sambanova.ai/v1/chat/completions",
            "代理端点": "https://api-proxy.me/sambanova/v1/chat/completions"
        }
        
        # 测试用的API Key（需要替换为真实的）
        self.api_keys = [
            "f3bbe695-fcee-4a13-a9ea-7009654ed346",
            "8225648a-17df-4c13-80c9-7a34af3d7814", 
            "863523ec-c96b-4c9e-a845-e89007dcbc21",
            "96429cd5-8d87-4e7b-86aa-70225cb638b6",
            "d2cca8a9-fe87-400a-b427-d012583ed6a5",
            "9b4c9491-4007-4cdf-9f18-851fe466b515",
            "c9c1a032-90f6-489a-b994-876a68ca74f7"
        ]
        
        # 测试消息
        self.test_messages = [
            {"role": "user", "content": "Hello, how are you?"},
            {"role": "user", "content": "What is the capital of France?"},
            {"role": "user", "content": "Explain quantum computing in simple terms."},
            {"role": "user", "content": "Write a short poem about AI."},
            {"role": "user", "content": "What are the benefits of renewable energy?"}
        ]
        
        # 测试配置
        self.test_config = {
            "model": "Meta-Llama-3.1-8B-Instruct",
            "max_tokens": 100,
            "temperature": 0.7,
            "stream": False
        }
    
    def create_request_payload(self, message: Dict) -> Dict:
        """创建请求负载"""
        return {
            "model": self.test_config["model"],
            "messages": [message],
            "max_tokens": self.test_config["max_tokens"],
            "temperature": self.test_config["temperature"],
            "stream": self.test_config["stream"]
        }
    
    def test_single_request(self, endpoint_url: str, api_key: str, message: Dict) -> Dict:
        """测试单个请求"""
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        
        payload = self.create_request_payload(message)
        
        try:
            start_time = time.time()
            
            with httpx.Client(timeout=30) as client:
                response = client.post(endpoint_url, headers=headers, json=payload)
            
            end_time = time.time()
            response_time = (end_time - start_time) * 1000  # 转换为毫秒
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    content = data.get("choices", [{}])[0].get("message", {}).get("content", "")
                    return {
                        "success": True,
                        "response_time": response_time,
                        "status_code": response.status_code,
                        "content_length": len(content),
                        "content": content[:100] + "..." if len(content) > 100 else content
                    }
                except json.JSONDecodeError:
                    return {
                        "success": False,
                        "response_time": response_time,
                        "status_code": response.status_code,
                        "error": "JSON解析失败"
                    }
            else:
                return {
                    "success": False,
                    "response_time": response_time,
                    "status_code": response.status_code,
                    "error": response.text[:200]
                }
                
        except httpx.TimeoutException:
            return {
                "success": False,
                "response_time": 30000,  # 超时设为30秒
                "status_code": 0,
                "error": "请求超时"
            }
        except Exception as e:
            return {
                "success": False,
                "response_time": 0,
                "status_code": 0,
                "error": str(e)
            }
    
    def test_endpoint_batch(self, endpoint_name: str, endpoint_url: str, test_count: int = 5) -> Dict:
        """批量测试单个端点"""
        print(f"\n🧪 测试 {endpoint_name}: {endpoint_url}")
        print("=" * 60)
        
        results = []
        success_count = 0
        response_times = []
        
        for i in range(test_count):
            api_key = self.api_keys[i % len(self.api_keys)]
            message = self.test_messages[i % len(self.test_messages)]
            
            print(f"📋 测试 {i+1}/{test_count}: {message['content'][:30]}...")
            
            result = self.test_single_request(endpoint_url, api_key, message)
            results.append(result)
            
            if result["success"]:
                success_count += 1
                response_times.append(result["response_time"])
                print(f"✅ 成功 - {result['response_time']:.0f}ms - {result['content'][:50]}...")
            else:
                print(f"❌ 失败 - {result['status_code']} - {result['error'][:50]}...")
            
            # 避免频率限制
            if i < test_count - 1:
                time.sleep(2)
        
        # 计算统计数据
        stats = {
            "endpoint_name": endpoint_name,
            "endpoint_url": endpoint_url,
            "total_tests": test_count,
            "success_count": success_count,
            "success_rate": (success_count / test_count) * 100,
            "response_times": response_times,
            "avg_response_time": statistics.mean(response_times) if response_times else 0,
            "min_response_time": min(response_times) if response_times else 0,
            "max_response_time": max(response_times) if response_times else 0,
            "median_response_time": statistics.median(response_times) if response_times else 0
        }
        
        print(f"\n📊 {endpoint_name} 测试结果:")
        print(f"   成功率: {stats['success_rate']:.1f}% ({success_count}/{test_count})")
        if response_times:
            print(f"   平均响应时间: {stats['avg_response_time']:.0f}ms")
            print(f"   最快响应: {stats['min_response_time']:.0f}ms")
            print(f"   最慢响应: {stats['max_response_time']:.0f}ms")
            print(f"   中位数: {stats['median_response_time']:.0f}ms")
        
        return stats
    
    def run_speed_comparison(self, test_count: int = 5):
        """运行速度对比测试"""
        print("🚀 SambaNova API 速度对比测试")
        print("=" * 60)
        print(f"📋 测试配置:")
        print(f"   模型: {self.test_config['model']}")
        print(f"   最大令牌: {self.test_config['max_tokens']}")
        print(f"   温度: {self.test_config['temperature']}")
        print(f"   每个端点测试次数: {test_count}")
        print(f"   可用API Keys: {len(self.api_keys)}")
        
        all_results = {}
        
        # 测试每个端点
        for endpoint_name, endpoint_url in self.endpoints.items():
            try:
                result = self.test_endpoint_batch(endpoint_name, endpoint_url, test_count)
                all_results[endpoint_name] = result
            except Exception as e:
                print(f"❌ {endpoint_name} 测试异常: {e}")
                all_results[endpoint_name] = {
                    "endpoint_name": endpoint_name,
                    "error": str(e)
                }
        
        # 生成对比报告
        self.generate_comparison_report(all_results)
        
        return all_results
    
    def generate_comparison_report(self, results: Dict):
        """生成对比报告"""
        print("\n" + "=" * 60)
        print("📊 速度对比报告")
        print("=" * 60)
        
        # 提取有效结果
        valid_results = {k: v for k, v in results.items() if "avg_response_time" in v}
        
        if len(valid_results) < 2:
            print("❌ 没有足够的有效结果进行对比")
            return
        
        # 按平均响应时间排序
        sorted_results = sorted(valid_results.items(), key=lambda x: x[1]["avg_response_time"])
        
        print("\n🏆 速度排名:")
        for i, (name, data) in enumerate(sorted_results, 1):
            print(f"   {i}. {name}")
            print(f"      平均响应时间: {data['avg_response_time']:.0f}ms")
            print(f"      成功率: {data['success_rate']:.1f}%")
            print(f"      中位数响应时间: {data['median_response_time']:.0f}ms")
        
        # 计算速度差距
        if len(sorted_results) >= 2:
            fastest = sorted_results[0][1]
            slowest = sorted_results[-1][1]
            
            speed_diff = slowest["avg_response_time"] - fastest["avg_response_time"]
            speed_ratio = slowest["avg_response_time"] / fastest["avg_response_time"] if fastest["avg_response_time"] > 0 else 0
            
            print(f"\n⚡ 速度差距分析:")
            print(f"   最快端点: {sorted_results[0][0]} ({fastest['avg_response_time']:.0f}ms)")
            print(f"   最慢端点: {sorted_results[-1][0]} ({slowest['avg_response_time']:.0f}ms)")
            print(f"   时间差距: {speed_diff:.0f}ms")
            print(f"   速度倍数: {speed_ratio:.2f}x")
            
            if speed_diff > 1000:
                print(f"   💡 建议: 优先使用 {sorted_results[0][0]}")
            elif speed_diff > 500:
                print(f"   💡 建议: {sorted_results[0][0]} 明显更快")
            else:
                print(f"   💡 建议: 两个端点速度相近")


def main():
    """主函数"""
    tester = SambaNovaSpeedTester()
    
    print("🌐 SambaNova API 速度测试工具")
    print("=" * 60)
    
    # 检查API Keys
    if not tester.api_keys:
        print("❌ 没有可用的API Keys")
        print("💡 请在脚本中添加有效的API Keys")
        return
    
    try:
        test_count = int(input("请输入每个端点的测试次数 (默认5): ") or "5")
        if test_count <= 0:
            test_count = 5
    except ValueError:
        test_count = 5
    
    # 运行测试
    results = tester.run_speed_comparison(test_count)
    
    print(f"\n🎉 测试完成！")
    print(f"📄 详细结果已显示在上方")


if __name__ == "__main__":
    main()
