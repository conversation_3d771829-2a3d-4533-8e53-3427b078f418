#!/usr/bin/env python3
"""
机场订阅链接代理管理器
支持解析订阅链接并提供HTTP代理切换功能
"""

import httpx
import base64
import json
import random
import time
import urllib.parse
from typing import List, Dict, Optional

class AirportProxyManager:
    def __init__(self, subscription_url: str):
        """
        初始化机场代理管理器
        
        Args:
            subscription_url: 机场订阅链接
        """
        self.subscription_url = subscription_url
        self.proxies = []
        self.current_proxy_index = 0
        self.failed_proxies = set()
        
    def fetch_subscription(self) -> str:
        """获取订阅内容"""
        print(f"🔗 获取订阅链接: {self.subscription_url}")

        # 尝试多种方法获取订阅
        methods = [
            self._fetch_with_httpx,
            self._fetch_with_requests,
            self._fetch_with_urllib
        ]

        for method in methods:
            try:
                content = method()
                if content:
                    print(f"✅ 订阅内容长度: {len(content)} 字符")
                    return content
            except Exception as e:
                print(f"⚠️ 方法失败: {e}")
                continue

        print("❌ 所有获取方法都失败了")
        return ""

    def _fetch_with_httpx(self) -> str:
        """使用httpx获取"""
        with httpx.Client(
            timeout=30,
            verify=False,  # 忽略SSL验证
            headers={'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'}
        ) as client:
            response = client.get(self.subscription_url)
            if response.status_code == 200:
                return response.text
        return ""

    def _fetch_with_requests(self) -> str:
        """使用requests获取"""
        import requests
        response = requests.get(
            self.subscription_url,
            timeout=30,
            verify=False,
            headers={'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'}
        )
        if response.status_code == 200:
            return response.text
        return ""

    def _fetch_with_urllib(self) -> str:
        """使用urllib获取"""
        import urllib.request
        import ssl

        # 创建忽略SSL的上下文
        ctx = ssl.create_default_context()
        ctx.check_hostname = False
        ctx.verify_mode = ssl.CERT_NONE

        req = urllib.request.Request(
            self.subscription_url,
            headers={'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'}
        )

        with urllib.request.urlopen(req, context=ctx, timeout=30) as response:
            return response.read().decode('utf-8')

        return ""
    
    def decode_base64_content(self, content: str) -> str:
        """解码Base64内容"""
        try:
            # 尝试Base64解码
            decoded = base64.b64decode(content).decode('utf-8')
            print(f"✅ 解码成功，找到 {len(decoded.splitlines())} 个代理配置")
            return decoded
        except Exception as e:
            print(f"⚠️ Base64解码失败: {e}")
            return content
    
    def parse_vless_proxy(self, config_line: str) -> Optional[Dict]:
        """解析VLESS代理配置"""
        try:
            if not config_line.startswith('vless://'):
                return None
                
            # 移除协议前缀
            config = config_line[8:]
            
            # 分离用户信息和服务器信息
            if '@' not in config:
                return None
                
            user_part, server_part = config.split('@', 1)
            
            # 分离服务器地址和参数
            if '?' in server_part:
                server_addr, params = server_part.split('?', 1)
            else:
                server_addr = server_part
                params = ""
            
            # 解析服务器地址和端口
            if ':' in server_addr:
                host, port = server_addr.rsplit(':', 1)
                port = int(port.split('#')[0])  # 移除fragment
            else:
                return None
            
            # 解析参数
            param_dict = {}
            if params:
                # 移除fragment部分
                if '#' in params:
                    params = params.split('#')[0]
                    
                for param in params.split('&'):
                    if '=' in param:
                        key, value = param.split('=', 1)
                        param_dict[key] = urllib.parse.unquote(value)
            
            # 检查是否支持HTTP代理
            if param_dict.get('type') == 'http' or param_dict.get('network') == 'http':
                return {
                    'type': 'vless',
                    'host': host,
                    'port': port,
                    'proxy': f"{host}:{port}",
                    'params': param_dict
                }
            
            return None
            
        except Exception as e:
            print(f"⚠️ VLESS解析失败: {e}")
            return None
    
    def parse_trojan_proxy(self, config_line: str) -> Optional[Dict]:
        """解析Trojan代理配置"""
        try:
            if not config_line.startswith('trojan://'):
                return None
                
            # 移除协议前缀
            config = config_line[9:]
            
            # 分离密码和服务器信息
            if '@' not in config:
                return None
                
            password, server_part = config.split('@', 1)
            
            # 分离服务器地址和参数
            if '?' in server_part:
                server_addr, params = server_part.split('?', 1)
            else:
                server_addr = server_part
                params = ""
            
            # 解析服务器地址和端口
            if ':' in server_addr:
                host, port = server_addr.rsplit(':', 1)
                port = int(port.split('#')[0])  # 移除fragment
            else:
                return None
            
            # 对于Trojan，通常443端口可以作为HTTP代理使用
            if port == 443:
                return {
                    'type': 'trojan',
                    'host': host,
                    'port': port,
                    'proxy': f"{host}:{port}",
                    'password': password
                }
            
            return None
            
        except Exception as e:
            print(f"⚠️ Trojan解析失败: {e}")
            return None
    
    def parse_subscription_content(self, content: str) -> List[Dict]:
        """解析订阅内容"""
        proxies = []
        
        # 先尝试Base64解码
        decoded_content = self.decode_base64_content(content)
        
        lines = decoded_content.strip().split('\n')
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
                
            # 解析VLESS
            vless_proxy = self.parse_vless_proxy(line)
            if vless_proxy:
                proxies.append(vless_proxy)
                continue
                
            # 解析Trojan
            trojan_proxy = self.parse_trojan_proxy(line)
            if trojan_proxy:
                proxies.append(trojan_proxy)
                continue
        
        print(f"📊 成功解析 {len(proxies)} 个HTTP代理配置")
        return proxies
    
    def load_proxies(self) -> bool:
        """加载代理列表"""
        content = self.fetch_subscription()
        if not content:
            return False
            
        self.proxies = self.parse_subscription_content(content)
        
        if self.proxies:
            print(f"✅ 加载成功，共 {len(self.proxies)} 个代理")
            for i, proxy in enumerate(self.proxies[:5], 1):  # 显示前5个
                print(f"  {i}. {proxy['proxy']} ({proxy['type'].upper()})")
            if len(self.proxies) > 5:
                print(f"  ... 还有 {len(self.proxies) - 5} 个代理")
            return True
        else:
            print("❌ 没有找到可用的HTTP代理")
            return False
    
    def get_current_proxy(self) -> Optional[str]:
        """获取当前代理"""
        if not self.proxies:
            return None
            
        if self.current_proxy_index >= len(self.proxies):
            self.current_proxy_index = 0
            
        proxy = self.proxies[self.current_proxy_index]
        return proxy['proxy']
    
    def switch_to_next_proxy(self) -> Optional[str]:
        """切换到下一个代理"""
        if not self.proxies:
            return None
            
        # 标记当前代理为失败
        current_proxy = self.get_current_proxy()
        if current_proxy:
            self.failed_proxies.add(current_proxy)
            print(f"❌ 标记代理失败: {current_proxy}")
        
        # 寻找下一个可用代理
        attempts = 0
        while attempts < len(self.proxies):
            self.current_proxy_index = (self.current_proxy_index + 1) % len(self.proxies)
            next_proxy = self.get_current_proxy()
            
            if next_proxy and next_proxy not in self.failed_proxies:
                print(f"🔄 切换到新代理: {next_proxy}")
                return next_proxy
                
            attempts += 1
        
        print("❌ 没有可用的代理了")
        return None
    
    def test_proxy(self, proxy: str, test_url: str = "https://httpbin.org/ip") -> bool:
        """测试代理是否可用"""
        try:
            with httpx.Client(
                proxy=f"http://{proxy}",
                timeout=10
            ) as client:
                response = client.get(test_url)
                
            if response.status_code == 200:
                print(f"✅ 代理测试成功: {proxy}")
                return True
            else:
                print(f"❌ 代理测试失败: {proxy} (状态码: {response.status_code})")
                return False
                
        except Exception as e:
            print(f"❌ 代理测试异常: {proxy} - {e}")
            return False
    
    def get_working_proxy(self, max_tests: int = 5) -> Optional[str]:
        """获取一个可用的代理"""
        if not self.proxies:
            return None
            
        tested = 0
        while tested < max_tests and tested < len(self.proxies):
            proxy = self.get_current_proxy()
            if proxy and proxy not in self.failed_proxies:
                if self.test_proxy(proxy):
                    return proxy
                else:
                    self.failed_proxies.add(proxy)
            
            self.switch_to_next_proxy()
            tested += 1
        
        return None


def main():
    """测试机场代理管理器"""
    subscription_url = "https://52pokemon.xz61.cn/api/v1/client/subscribe?token=f8aaf630e7f05242986d7382f8dd57de"
    
    print("🌐 机场代理管理器测试")
    print("=" * 50)
    
    manager = AirportProxyManager(subscription_url)
    
    # 加载代理
    if manager.load_proxies():
        print(f"\n📋 代理管理器状态:")
        print(f"   总代理数: {len(manager.proxies)}")
        print(f"   当前代理: {manager.get_current_proxy()}")
        
        # 测试代理切换
        print(f"\n🔄 测试代理切换:")
        for i in range(3):
            proxy = manager.switch_to_next_proxy()
            if proxy:
                print(f"   第 {i+1} 次切换: {proxy}")
            else:
                break
        
        # 测试获取可用代理
        print(f"\n🧪 测试获取可用代理:")
        working_proxy = manager.get_working_proxy(max_tests=3)
        if working_proxy:
            print(f"✅ 找到可用代理: {working_proxy}")
        else:
            print("❌ 没有找到可用代理")
    
    else:
        print("❌ 代理加载失败")


if __name__ == "__main__":
    main()
