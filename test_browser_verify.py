#!/usr/bin/env python3
"""
测试浏览器验证功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from SambaNova_Auto import SambaNovaAuto

def test_browser_verification():
    """测试浏览器验证功能"""
    print("🧪 测试浏览器验证功能...")
    
    sambanova = SambaNovaAuto()
    
    # 使用一个示例验证链接进行测试
    test_link = "https://auth0.sambanova.ai/u/email-verification?ticket=test123"
    
    print(f"🔗 测试验证链接: {test_link}")
    
    try:
        result = sambanova.verify_email_with_browser(test_link)
        
        if result:
            print("✅ 浏览器验证功能测试通过")
        else:
            print("⚠️ 浏览器验证功能测试失败（这是预期的，因为使用的是测试链接）")
        
        return True
        
    except Exception as e:
        print(f"❌ 浏览器验证功能测试异常: {e}")
        return False

def main():
    """主测试函数"""
    print("🔧 浏览器验证功能测试")
    print("=" * 50)
    
    success = test_browser_verification()
    
    if success:
        print("\n🎉 测试完成！浏览器验证功能可用")
        print("💡 现在可以使用真实的验证链接进行邮箱验证")
    else:
        print("\n❌ 测试失败！请检查 Playwright 安装")

if __name__ == "__main__":
    main()
