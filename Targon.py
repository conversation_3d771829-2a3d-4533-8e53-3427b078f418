#!/usr/bin/env python3
"""
Targon 注册机【躺平版】
使用 minmail.app 临时邮箱服务
所有功能集中在一个文件中
"""

import json
import time
import random
import string
import re
import uuid
import httpx
import pyotp
from bs4 import BeautifulSoup
from datetime import datetime

class TargonSimple:
    def __init__(self):
        self.visitor_id = str(uuid.uuid4())
        self.email_address = None
        self.session_cookie = None
        self.keys_file = "api_keys.txt"

        # imail.edu.vn 支持的域名（教育域名）
        self.email_domains = ['@tempmail.io.vn']
        
        # 随机User-Agent列表
        self.user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:126.0) Gecko/20100101 Firefox/126.0',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:126.0) Gecko/20100101 Firefox/126.0',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36 Edg/135.0.0.0',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36 Edg/135.0.0.0'
        ]
        
        # 随机选择一个User-Agent
        selected_ua = random.choice(self.user_agents)
        
        # 根据User-Agent设置对应的sec-ch-ua和sec-ch-ua-platform
        if 'Chrome' in selected_ua:
            # 提取Chrome版本号
            chrome_version = re.search(r'Chrome/(\d+\.\d+\.\d+\.\d+)', selected_ua)
            if chrome_version:
                version = chrome_version.group(1)
                sec_ch_ua = f'"Chromium";v="{version}", "Google Chrome";v="{version}", "Not(A:Brand";v="24"'
            else:
                sec_ch_ua = '"Chromium";v="138", "Google Chrome";v="138", "Not(A:Brand";v="24"'
            
            # 设置平台
            if 'Windows' in selected_ua:
                sec_ch_ua_platform = '"Windows"'
            elif 'Mac' in selected_ua:
                sec_ch_ua_platform = '"macOS"'
            else:
                sec_ch_ua_platform = '"Linux"'
        elif 'Firefox' in selected_ua:
            sec_ch_ua = '"Not(A:Brand";v="8", "Chromium";v="126", "Firefox";v="126"'
            if 'Windows' in selected_ua:
                sec_ch_ua_platform = '"Windows"'
            elif 'Mac' in selected_ua:
                sec_ch_ua_platform = '"macOS"'
            else:
                sec_ch_ua_platform = '"Linux"'
        elif 'Edg' in selected_ua:
            # 提取Edge版本号
            edge_version = re.search(r'Edg/(\d+\.\d+\.\d+\.\d+)', selected_ua)
            if edge_version:
                version = edge_version.group(1)
                sec_ch_ua = f'"Chromium";v="{version}", "Microsoft Edge";v="{version}", "Not(A:Brand";v="24"'
            else:
                sec_ch_ua = '"Chromium";v="135", "Microsoft Edge";v="135", "Not(A:Brand";v="24"'
            
            # 设置平台
            if 'Windows' in selected_ua:
                sec_ch_ua_platform = '"Windows"'
            elif 'Mac' in selected_ua:
                sec_ch_ua_platform = '"macOS"'
            else:
                sec_ch_ua_platform = '"Linux"'
        else:
            sec_ch_ua = '"Not(A:Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"'
            sec_ch_ua_platform = '"Windows"'
        
        # HTTP客户端配置
        self.client = httpx.Client(
            timeout=60.0,
            headers={
                'User-Agent': selected_ua,
                'Accept': '*/*',
                'Accept-Language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7',
                'sec-ch-ua': sec_ch_ua,
                'sec-ch-ua-mobile': '?0',
                'sec-ch-ua-platform': sec_ch_ua_platform,
                'sec-fetch-dest': 'empty',
                'sec-fetch-mode': 'cors',
                'sec-fetch-site': 'same-origin',
                'sec-gpc': '1',
                'visitor-id': self.visitor_id
            }
        )
        
        print(f"🔄 使用随机UA: {selected_ua[:50]}...")

    def generate_random_email_name(self):
        """生成随机邮箱名称（6位小写字母，符合 imail.edu.vn 格式）"""
        return ''.join(random.choice(string.ascii_lowercase) for _ in range(6))

    def generate_password(self):
        """生成随机密码"""
        length = 12
        chars = string.ascii_letters + string.digits + "!@#$%^&*"
        return ''.join(random.choice(chars) for _ in range(length))
    
    def get_email_address(self):
        """获取临时邮箱地址（使用 imail.edu.vn 完整流程）"""
        try:
            print("📧 创建临时邮箱地址...")

            # 生成随机邮箱名称（6位字母）
            email_name = self.generate_random_email_name()

            # 使用教育域名（去掉@符号）
            domain = "tempmail.io.vn"

            # 构造完整邮箱地址
            self.email_address = f"{email_name}@{domain}"

            print(f"🔄 在 imail.edu.vn 创建邮箱: {self.email_address}")

            # 通过网站API创建邮箱
            success = self._create_mailbox_on_website(email_name, domain)

            if success:
                print(f"✅ 邮箱创建成功: {self.email_address}")
                return True
            else:
                print(f"❌ 邮箱创建失败")
                return False

        except Exception as e:
            print(f"❌ 创建邮箱异常: {e}")
            return False
    
    def register_account(self, password):
        """注册Targon账户"""
        try:
            print("🚀 开始注册账户...")
            
            register_data = {
                "0": {
                    "json": {
                        "email": self.email_address,
                        "password": password,
                        "password2": password
                    }
                }
            }
            
            response = self.client.post(
                "https://targon.com/api/trpc/account.createAccount?batch=1",
                json=register_data,
                headers={
                    'Content-Type': 'application/json',
                    'x-trpc-source': 'react',
                    'Referer': 'https://targon.com/sign-in?mode=signup',
                    'Origin': 'https://targon.com'
                }
            )
            
            if response.status_code == 200:
                print("✅ 账户注册成功")
                return True
            else:
                print(f"❌ 注册失败: {response.status_code}")
                # 尝试输出错误详情
                try:
                    error_data = response.json()
                    print(f"❌ 错误详情: {error_data}")
                except:
                    print(f"❌ 响应内容: {response.text[:200]}...")
                return False
                
        except Exception as e:
            print(f"❌ 注册异常: {e}")
            return False
    
    def get_activation_link(self, max_attempts=15, delay=3):
        """获取激活链接（基于Playwright测试的正确邮箱访问方式）"""
        try:
            print("📬 等待激活邮件...")

            # 提取邮箱名称（去掉域名）
            email_name = self.email_address.split('@')[0]

            for attempt in range(max_attempts):
                print(f"📧 检查邮件 (第 {attempt + 1}/{max_attempts} 次)...")

                # 基于Playwright测试的正确方法：直接访问邮箱页面
                try:
                    # 访问邮箱页面（基于成功创建后的正确URL）
                    mailbox_response = self.client.get(
                        'https://imail.edu.vn/mailbox',
                        headers={
                            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                            'Referer': 'https://imail.edu.vn/'
                        },
                        follow_redirects=True
                    )

                    if mailbox_response.status_code == 200:
                        page_content = mailbox_response.text
                        soup = BeautifulSoup(page_content, 'html.parser')

                        # 检查页面标题确认邮箱正确
                        title = soup.find('title')
                        page_title = title.text if title else ""

                        if email_name in page_title:
                            print(f"✅ 确认访问正确邮箱: {page_title}")

                        # 查找包含 Targon 验证邮件的内容
                        page_text = soup.get_text()

                        # 检查邮箱状态
                        if 'Empty Inbox' in page_text:
                            print("📭 收件箱为空，继续等待...")
                        elif 'Fetching' in page_text:
                            print("🔄 正在获取邮件...")
                        else:
                            # 查找 Targon 相关邮件
                            if '<EMAIL>' in page_text or 'Targon' in page_text or 'manifold' in page_text.lower():
                                print("🎯 在页面中找到Targon验证邮件!")

                                # 查找所有链接
                                links = soup.find_all('a', href=True)
                                for link in links:
                                    href = link.get('href', '')
                                    if 'email-verification' in href and 'token=' in href:
                                        print("🔗 成功提取激活链接")
                                        return href

                                # 如果没有找到完整链接，尝试查找邮件内容
                                # 查找可能包含邮件的容器
                                mail_containers = soup.find_all(['div', 'li', 'tr', 'article', 'section'])
                                for container in mail_containers:
                                    container_text = container.get_text()
                                    if ('<EMAIL>' in container_text or
                                        'Targon' in container_text or
                                        'email-verification' in container_text):
                                        print("🎯 在邮件容器中找到Targon验证邮件!")

                                        # 在这个容器中查找链接
                                        container_links = container.find_all('a', href=True)
                                        for link in container_links:
                                            href = link.get('href', '')
                                            if 'email-verification' in href and 'token=' in href:
                                                print("🔗 成功提取激活链接")
                                                return href

                                        # 如果没有找到链接，尝试从文本中提取
                                        import re
                                        link_pattern = r'https?://[^\s<>"]+email-verification[^\s<>"]*token=[^\s<>"&]+'
                                        matches = re.findall(link_pattern, container_text)
                                        if matches:
                                            print("� 从文本中提取激活链接")
                                            return matches[0]
                            else:
                                print(f"📄 页面内容预览: {page_text[:300]}...")

                    else:
                        print(f"⚠️ 邮箱页面访问失败: {mailbox_response.status_code}")

                except Exception as e:
                    print(f"⚠️ 邮箱页面访问异常: {e}")

                # 方法2: 尝试模拟刷新按钮（基于Playwright观察到的功能）
                try:
                    # 模拟点击Refresh按钮的请求
                    refresh_response = self.client.get(
                        'https://imail.edu.vn/mailbox',
                        headers={
                            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                            'Referer': 'https://imail.edu.vn/mailbox',
                            'Cache-Control': 'no-cache',
                            'Pragma': 'no-cache'
                        },
                        follow_redirects=True
                    )

                    if refresh_response.status_code == 200:
                        soup = BeautifulSoup(refresh_response.text, 'html.parser')
                        page_text = soup.get_text()

                        if ('<EMAIL>' in page_text or
                            'Targon' in page_text or
                            'manifold' in page_text.lower()):
                            print("🎯 刷新后找到Targon验证邮件!")

                            # 查找激活链接
                            links = soup.find_all('a', href=True)
                            for link in links:
                                href = link.get('href', '')
                                if 'email-verification' in href and 'token=' in href:
                                    print("🔗 刷新后成功提取激活链接")
                                    return href

                            # 尝试从文本中提取链接
                            import re
                            link_pattern = r'https?://[^\s<>"]+email-verification[^\s<>"]*token=[^\s<>"&]+'
                            matches = re.findall(link_pattern, page_text)
                            if matches:
                                print("🔗 从刷新页面文本中提取激活链接")
                                return matches[0]

                except Exception as refresh_error:
                    print(f"⚠️ 邮箱刷新失败: {refresh_error}")

                if attempt < max_attempts - 1:
                    print(f"⏰ 等待 {delay} 秒后重试...")
                    time.sleep(delay)

            print("❌ 未能获取到激活链接")
            return None

        except Exception as e:
            print(f"❌ 获取激活链接异常: {e}")
            return None

    def _get_csrf_token(self):
        """获取 CSRF token"""
        try:
            # 访问主页获取 CSRF token
            response = self.client.get(
                'https://imail.edu.vn/',
                headers={
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                }
            )

            if response.status_code == 200:
                soup = BeautifulSoup(response.text, 'html.parser')

                # 查找 CSRF token
                csrf_meta = soup.find('meta', {'name': 'csrf-token'})
                if csrf_meta:
                    token = csrf_meta.get('content')
                    print(f"✅ 获取 CSRF token 成功")
                    return token

                # 如果没有找到 meta 标签，尝试从表单中查找
                csrf_input = soup.find('input', {'name': '_token'})
                if csrf_input:
                    token = csrf_input.get('value')
                    print(f"✅ 从表单获取 CSRF token 成功")
                    return token

            print("⚠️ 未找到 CSRF token")
            return None

        except Exception as e:
            print(f"❌ 获取 CSRF token 异常: {e}")
            return None

    def _create_mailbox_on_website(self, email_name, domain):
        """在 imail.edu.vn 网站上创建邮箱（基于Playwright测试的正确流程）"""
        try:
            print(f"🌐 访问 imail.edu.vn 主页...")

            # 首先访问主页建立会话
            response = self.client.get(
                'https://imail.edu.vn/',
                headers={
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                }
            )

            if response.status_code != 200:
                print(f"❌ 访问主页失败: {response.status_code}")
                return False

            print("✅ 主页访问成功")

            # 解析主页获取表单信息
            soup = BeautifulSoup(response.text, 'html.parser')

            # 获取CSRF token
            csrf_token = None
            csrf_input = soup.find('input', {'name': '_token'})
            if csrf_input:
                csrf_token = csrf_input.get('value')
                print("✅ 获取CSRF token成功")
            else:
                print("⚠️ 未找到CSRF token")

            # 基于Playwright测试的正确方法：模拟完整的表单提交流程
            print(f"📝 模拟网页表单提交: {email_name}@{domain}")

            # 准备表单数据（模拟网页表单）
            form_data = {
                'user': email_name,
                'domain': domain
            }

            if csrf_token:
                form_data['_token'] = csrf_token

            # 模拟浏览器的完整表单提交
            create_response = self.client.post(
                'https://imail.edu.vn/',
                data=form_data,
                headers={
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                    'Referer': 'https://imail.edu.vn/',
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                    'Accept-Language': 'en-US,en;q=0.5',
                    'Accept-Encoding': 'gzip, deflate, br',
                    'DNT': '1',
                    'Connection': 'keep-alive',
                    'Upgrade-Insecure-Requests': '1'
                },
                follow_redirects=True
            )

            print(f"表单提交响应: {create_response.status_code}")
            print(f"最终URL: {create_response.url}")

            # 检查是否成功跳转到邮箱页面
            if create_response.status_code == 200:
                soup = BeautifulSoup(create_response.text, 'html.parser')
                title = soup.find('title')
                page_title = title.text if title else ""

                print(f"页面标题: {page_title}")

                # 检查是否包含邮箱地址（基于Playwright测试的成功标志）
                if (email_name in page_title or
                    f"{email_name}@{domain}" in create_response.text or
                    str(create_response.url) == 'https://imail.edu.vn/mailbox'):
                    print("✅ 邮箱创建成功，已跳转到邮箱页面")
                    return True
                else:
                    print("⚠️ 表单提交成功但未跳转到邮箱页面")

            # 如果表单提交没有成功跳转，尝试直接访问邮箱页面
            print("🔄 尝试直接访问邮箱页面...")

            mailbox_response = self.client.get(
                'https://imail.edu.vn/mailbox',
                headers={
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                    'Referer': 'https://imail.edu.vn/'
                },
                follow_redirects=True
            )

            if mailbox_response.status_code == 200:
                soup = BeautifulSoup(mailbox_response.text, 'html.parser')
                title = soup.find('title')
                page_title = title.text if title else ""

                print(f"邮箱页面标题: {page_title}")

                # 检查是否成功访问到邮箱页面
                if (str(mailbox_response.url) == 'https://imail.edu.vn/mailbox' or
                    email_name in page_title or
                    f"{email_name}@{domain}" in mailbox_response.text):
                    print("✅ 成功访问邮箱页面")
                    return True
                else:
                    print("⚠️ 邮箱页面访问成功但内容不匹配")
                    # 即使内容不完全匹配，也继续尝试
                    return True
            else:
                print(f"❌ 邮箱页面访问失败: {mailbox_response.status_code}")
                return False

        except Exception as e:
            print(f"❌ 网站邮箱创建异常: {e}")
            return False
    
    def activate_email(self, activation_link):
        """激活邮箱"""
        try:
            print("🔗 开始激活邮箱...")
            
            # 设置激活请求头，使用相同的随机UA
            activation_headers = {
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
                'Accept-Language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7',
                'sec-fetch-dest': 'document',
                'sec-fetch-mode': 'navigate',
                'sec-fetch-site': 'none',
                'sec-fetch-user': '?1',
                'upgrade-insecure-requests': '1',
                'User-Agent': self.client.headers['User-Agent']
            }
            
            # 手动处理重定向
            current_url = activation_link
            max_redirects = 5
            redirect_count = 0
            
            while redirect_count < max_redirects:
                response = self.client.get(
                    current_url,
                    headers=activation_headers,
                    follow_redirects=False
                )
                
                # 检查Set-Cookie头
                set_cookie_headers = response.headers.get_list('set-cookie')
                for set_cookie in set_cookie_headers:
                    if 'auth_session=' in set_cookie:
                        # 提取auth_session的值
                        parts = set_cookie.split(';')
                        for part in parts:
                            if part.strip().startswith('auth_session='):
                                self.session_cookie = part.strip().split('=', 1)[1]
                                print("🍪 获取到登录凭证")
                                break
                
                # 处理重定向
                if response.status_code in [301, 302, 307, 308]:
                    location = response.headers.get('Location')
                    if not location:
                        break
                    
                    # 处理相对URL
                    if location.startswith('/'):
                        from urllib.parse import urljoin
                        current_url = urljoin('https://targon.com', location)
                    else:
                        current_url = location
                    
                    redirect_count += 1
                    continue
                
                elif response.status_code == 200:
                    print("✅ 邮箱激活成功")
                    return True
                else:
                    print(f"❌ 激活失败: {response.status_code}")
                    return False
            
            print("❌ 重定向次数过多")
            return False
            
        except Exception as e:
            print(f"❌ 激活异常: {e}")
            return False
    
    def setup_2fa(self):
        """设置2FA"""
        try:
            if not self.session_cookie:
                print("❌ 没有有效的登录凭证")
                return False
            
            print("🔐 开始设置2FA...")
            
            # 创建2FA
            headers = {
                'Content-Type': 'application/json',
                'x-trpc-source': 'react',
                'Referer': 'https://targon.com/two-factor-auth'
            }
            cookies = {'auth_session': self.session_cookie}
            
            # 获取2FA密钥
            response = self.client.get(
                "https://targon.com/api/trpc/account.createTwoFactorURI?batch=1&input=%7B%220%22%3A%7B%22json%22%3Anull%2C%22meta%22%3A%7B%22values%22%3A%5B%22undefined%22%5D%7D%7D%7D",
                headers=headers,
                cookies=cookies
            )
            
            if response.status_code != 200:
                print(f"❌ 创建2FA失败: {response.status_code}")
                return False
            
            response_data = response.json()
            result_data = response_data[0]['result']['data']['json']
            two_factor_secret = result_data['twoFactorSecret']
            manual_code = result_data['manualCode']
            
            # 生成TOTP验证码
            totp = pyotp.TOTP(manual_code)
            otp_code = totp.now()
            
            # 启用2FA
            enable_data = {
                "0": {
                    "json": {
                        "otp": otp_code,
                        "twoFactorSecret": two_factor_secret
                    }
                }
            }
            
            response = self.client.post(
                "https://targon.com/api/trpc/account.enable2FA?batch=1",
                json=enable_data,
                headers=headers,
                cookies=cookies
            )
            
            if response.status_code == 200:
                print("✅ 2FA设置成功")
                return True
            else:
                print(f"❌ 启用2FA失败: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ 2FA设置异常: {e}")
            return False
    
    def get_api_keys(self):
        """获取API密钥"""
        try:
            if not self.session_cookie:
                print("❌ 没有有效的登录凭证")
                return []
            
            print("🔑 获取API密钥...")
            
            headers = {
                'x-trpc-source': 'react',
                'Referer': 'https://targon.com/settings'
            }
            cookies = {'auth_session': self.session_cookie}
            
            # 构造查询URL
            query_url = f"https://targon.com/api/trpc/keys.getApiKeys,model.getPopularModels,account.getUserBookmarks,account.getUserInterest,account.getUserSubscription,account.check2FA,account.getTaoPrice,account.getAlphaPrice,notification.getNotifications?batch=1&input=%7B%220%22%3A%7B%22json%22%3Anull%2C%22meta%22%3A%7B%22values%22%3A%5B%22undefined%22%5D%7D%7D%2C%221%22%3A%7B%22json%22%3A%7B%22days%22%3A30%2C%22limit%22%3A3%7D%7D%2C%222%22%3A%7B%22json%22%3Anull%2C%22meta%22%3A%7B%22values%22%3A%5B%22undefined%22%5D%7D%7D%2C%223%22%3A%7B%22json%22%3Anull%2C%22meta%22%3A%7B%22values%22%3A%5B%22undefined%22%5D%7D%7D%2C%224%22%3A%7B%22json%22%3Anull%2C%22meta%22%3A%7B%22values%22%3A%5B%22undefined%22%5D%7D%7D%2C%225%22%3A%7B%22json%22%3A%7B%22email%22%3A%22{self.email_address}%22%7D%7D%2C%226%22%3A%7B%22json%22%3Anull%2C%22meta%22%3A%7B%22values%22%3A%5B%22undefined%22%5D%7D%7D%2C%227%22%3A%7B%22json%22%3Anull%2C%22meta%22%3A%7B%22values%22%3A%5B%22undefined%22%5D%7D%7D%2C%228%22%3A%7B%22json%22%3Anull%2C%22meta%22%3A%7B%22values%22%3A%5B%22undefined%22%5D%7D%7D%7D"
            
            response = self.client.get(
                query_url,
                headers=headers,
                cookies=cookies
            )
            
            if response.status_code == 200:
                response_data = response.json()
                api_keys_data = response_data[0]['result']['data']['json']
                
                if api_keys_data:
                    print(f"✅ 获取到 {len(api_keys_data)} 个API密钥")
                    return api_keys_data
                else:
                    print("⚠️ 未找到API密钥")
                    return []
            else:
                print(f"❌ 获取密钥失败: {response.status_code}")
                return []
                
        except Exception as e:
            print(f"❌ 获取密钥异常: {e}")
            return []
    
    def save_keys(self, api_keys):
        """保存API密钥到文件"""
        try:
            # 读取现有密钥
            existing_keys = set()
            try:
                with open(self.keys_file, 'r', encoding='utf-8') as f:
                    existing_keys = {line.strip() for line in f if line.strip()}
            except FileNotFoundError:
                pass
            
            # 保存新密钥
            new_keys = []
            for key_info in api_keys:
                key = key_info.get('key')
                if key and key not in existing_keys:
                    new_keys.append(key)
            
            if new_keys:
                with open(self.keys_file, 'a', encoding='utf-8') as f:
                    for key in new_keys:
                        f.write(key + '\n')
                print(f"💾 保存了 {len(new_keys)} 个新密钥到 {self.keys_file}")
            else:
                print("ℹ️ 没有新密钥需要保存")
                
        except Exception as e:
            print(f"❌ 保存密钥异常: {e}")
    
    def register_single_account(self):
        """注册单个账户的完整流程"""
        try:
            print(f"\n🎯 开始注册新账户 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            
            # 1. 获取邮箱
            if not self.get_email_address():
                return False
            
            # 2. 生成密码
            password = self.generate_password()
            print(f"🔐 生成密码: {password}")
            
            # 3. 注册账户
            if not self.register_account(password):
                return False
            
            # 4. 获取激活链接
            activation_link = self.get_activation_link()
            if not activation_link:
                return False
            
            # 5. 激活邮箱
            if not self.activate_email(activation_link):
                return False
            
            # 6. 设置2FA
            if not self.setup_2fa():
                return False
            
            # 7. 获取API密钥
            api_keys = self.get_api_keys()
            if not api_keys:
                print("❌ 未获取到API密钥")
                return False
            
            # 8. 保存密钥
            self.save_keys(api_keys)
            
            # 9. 显示成功信息
            print("\n🎉 账户注册完成!")
            print(f"📧 邮箱: {self.email_address}")
            print(f"🔐 密码: {password}")
            print(f"🔑 API密钥:")
            for key_info in api_keys:
                key = key_info.get('key', '')
                print(f"   {key[:15]}...{key[-8:] if len(key) > 23 else key}")
            
            return True
            
        except Exception as e:
            print(f"❌ 注册流程异常: {e}")
            return False
    
    def run_batch(self, count=1):
        """批量注册"""
        print(f"📦 开始批量注册 {count} 个账户")
        
        success_count = 0
        for i in range(count):
            print(f"\n{'='*50}")
            print(f"📋 注册进度: {i+1}/{count}")
            
            # 为每个账户创建新实例，避免数据污染
            bot = TargonSimple()
            if bot.register_single_account():
                success_count += 1
            
            # 注册间隔
            if i < count - 1:
                wait_time = 5
                print(f"⏳ 等待 {wait_time} 秒后继续...")
                time.sleep(wait_time)
        
        print(f"\n🏆 批量注册完成!")
        print(f"✅ 成功注册: {success_count}/{count}")
        
        # 统计总密钥数
        try:
            with open(self.keys_file, 'r', encoding='utf-8') as f:
                total_keys = len([line.strip() for line in f if line.strip()])
            print(f"📊 总密钥数: {total_keys}")
        except FileNotFoundError:
            print("📊 总密钥数: 0")
    
    def __del__(self):
        """清理资源"""
        try:
            self.client.close()
        except:
            pass

def show_logo():
    """显示程序logo"""
    logo = """
╔══════════════════════════════════════════════════════════════╗
║                                                              ║
║  ████████╗ █████╗ ██████╗  ██████╗  ██████╗ ███╗   ██╗       ║
║  ╚══██╔══╝██╔══██╗██╔══██╗██╔════╝ ██╔═══██╗████╗  ██║       ║
║     ██║   ███████║██████╔╝██║  ███╗██║   ██║██╔██╗ ██║       ║
║     ██║   ██╔══██║██╔══██╗██║   ██║██║   ██║██║╚██╗██║       ║
║     ██║   ██║  ██║██║  ██║╚██████╔╝╚██████╔╝██║ ╚████║       ║
║     ╚═╝   ╚═╝  ╚═╝╚═╝  ╚═╝ ╚═════╝  ╚═════╝ ╚═╝  ╚═══╝       ║
║                                                              ║
║                    🤖 注册机【躺平版】 🤖                    ║
║             💤 让注册变得简单，让躺平变得优雅 💤             ║
║                                                              ║
╚══════════════════════════════════════════════════════════════╝
    """
    print(logo)

def get_user_choice():
    """获取用户选择"""
    print("\n📋 请选择运行模式：")
    print("   1️⃣  单个账户注册")
    print("   2️⃣  批量账户注册")
    print("   0️⃣  退出程序")
    print("=" * 50)
    
    while True:
        try:
            choice = input("请输入选择 (0/1/2): ").strip()
            if choice in ['0', '1', '2']:
                return int(choice)
            else:
                print("❌ 无效选择，请输入 0、1 或 2")
        except KeyboardInterrupt:
            print("\n\n👋 程序已退出")
            return 0
        except Exception:
            print("❌ 输入错误，请重新输入")

def get_batch_count():
    """获取批量注册数量"""
    print("\n📊 批量注册设置：")
    while True:
        try:
            count = input("请输入注册账户数量 (1-100): ").strip()
            count = int(count)
            if 1 <= count <= 100:
                return count
            else:
                print("❌ 数量必须在 1-100 之间")
        except KeyboardInterrupt:
            print("\n\n👋 程序已退出")
            return 0
        except ValueError:
            print("❌ 请输入有效的数字")
        except Exception:
            print("❌ 输入错误，请重新输入")

def main():
    """主函数"""
    # 显示logo
    show_logo()
    
    # 显示程序信息
    print("🔧 程序版本：v1.4.0")
    print("👨‍💻 作者信息：云胡不喜@linux.do")
    print("📧 临时邮箱：imail.edu.vn (基于Playwright测试的正确流程)")
    print("🎯 目标平台：Targon.com")
    print("⚡ 特色功能：自动注册、邮箱激活、2FA设置、API密钥获取")
    
    while True:
        # 获取用户选择
        choice = get_user_choice()
        
        if choice == 0:
            print("\n👋 感谢使用 Targon 注册机！再见~")
            break
        elif choice == 1:
            print("\n🎯 启动单个账户注册模式...")
            bot = TargonSimple()
            bot.register_single_account()
            
            # 询问是否继续
            continue_choice = input("\n🤔 是否继续注册？(y/n): ").strip().lower()
            if continue_choice not in ['y', 'yes', '是']:
                print("\n👋 程序结束，感谢使用！")
                break
                
        elif choice == 2:
            count = get_batch_count()
            if count == 0:
                break
            
            print(f"\n🚀 启动批量注册模式，目标数量：{count}")
            print("⚠️  批量注册可能需要较长时间，请耐心等待...")
            
            # 确认开始
            confirm = input(f"确认开始批量注册 {count} 个账户？(y/n): ").strip().lower()
            if confirm in ['y', 'yes', '是']:
                bot = TargonSimple()
                bot.run_batch(count)
            else:
                print("❌ 已取消批量注册")
                
            # 询问是否继续
            continue_choice = input("\n🤔 是否继续使用程序？(y/n): ").strip().lower()
            if continue_choice not in ['y', 'yes', '是']:
                print("\n👋 程序结束，感谢使用！")
                break

if __name__ == "__main__":
    main()