#!/usr/bin/env python3
"""
TempMail.Plus API 测试脚本
"""

import httpx
import random
import string
import time

def generate_random_email_name():
    """生成随机邮箱名称（7位小写字母）"""
    return ''.join(random.choice(string.ascii_lowercase) for _ in range(7))

def test_tempmail_api():
    """测试 TempMail.Plus API"""
    
    # 支持的域名列表
    email_domains = [
        '@mailto.plus',
        '@fexpost.com', 
        '@fexbox.org',
        '@mailbox.in.ua',
        '@rover.info',
        '@chitthi.in',
        '@fextemp.com',
        '@any.pink',
        '@merepost.com'
    ]
    
    # 生成测试邮箱
    email_name = generate_random_email_name()
    domain = random.choice(email_domains)
    test_email = email_name + domain
    
    print(f"🧪 测试邮箱: {test_email}")
    
    # 创建HTTP客户端
    client = httpx.Client(timeout=30.0)
    
    try:
        # 测试API调用
        print("📡 测试API调用...")
        response = client.get(
            'https://tempmail.plus/api/mails',
            params={
                'email': test_email,
                'limit': 20,
                'epin': ''
            },
            headers={
                'referer': 'https://tempmail.plus/',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
        )
        
        print(f"📊 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ API调用成功!")
            print(f"📋 响应数据结构:")
            print(f"   - count: {data.get('count', 'N/A')}")
            print(f"   - result: {data.get('result', 'N/A')}")
            print(f"   - mail_list: {len(data.get('mail_list', []))} 封邮件")
            print(f"   - more: {data.get('more', 'N/A')}")
            
            # 显示完整响应（用于调试）
            print(f"\n🔍 完整响应数据:")
            print(data)
            
            return True
        else:
            print(f"❌ API调用失败: {response.status_code}")
            print(f"❌ 响应内容: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False
    finally:
        client.close()

if __name__ == "__main__":
    print("🚀 开始测试 TempMail.Plus API...")
    success = test_tempmail_api()
    
    if success:
        print("\n✅ TempMail.Plus API 测试通过!")
    else:
        print("\n❌ TempMail.Plus API 测试失败!")
