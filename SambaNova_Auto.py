#!/usr/bin/env python3
"""
SambaNova AI 自动注册程序
完整自动化方案：注册 → 邮箱验证 → 登录获取 API Key
"""

import json
import time
import random
import string
import secrets
import urllib.parse
import httpx
from bs4 import BeautifulSoup
from fake_useragent import UserAgent
from datetime import datetime, timedelta

class SambaNovaAuto:
    def __init__(self):
        self.email_address = None
        self.password = None
        self.access_token = None
        self.keys_file = "sambanova_keys.txt"
        
        # HTTP客户端配置
        self.client = httpx.Client(
            timeout=60.0,
            headers={
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'Accept': '*/*',
                'Accept-Language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7'
            }
        )
        
        # SambaNova 配置
        self.config_data = None
        self.client_id = None
        self.issuer_base_url = None
        self.redirect_url = None
    
    def generate_random_string(self, length=5):
        """生成随机字符串"""
        letters = string.ascii_lowercase
        return ''.join(random.choice(letters) for _ in range(length))
    
    def generate_password(self):
        """生成随机密码"""
        length = 12
        chars = string.ascii_letters + string.digits + "!@#$%^&*"
        return ''.join(random.choice(chars) for _ in range(length))
    
    def get_email_address(self):
        """获取临时邮箱地址（使用 minmail.app）"""
        try:
            print("📧 获取临时邮箱地址...")
            
            response = self.client.get(
                'https://minmail.app/api/mail/address',
                params={
                    'refresh': 'true',
                    'expire': '1440',
                    'part': 'main'
                },
                headers={
                    'referer': 'https://minmail.app/'
                }
            )
            
            if response.status_code == 200:
                data = response.json()
                self.email_address = data.get('address')
                print(f"✅ 获取邮箱成功: {self.email_address}")
                return True
            else:
                print(f"❌ 获取邮箱失败: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ 获取邮箱异常: {e}")
            return False
    
    def get_sambanova_config(self):
        """获取 SambaNova 配置信息"""
        try:
            print("🔧 获取 SambaNova 配置...")
            
            ua = UserAgent()
            headers = {
                "accept": "*/*",
                "accept-language": "zh-CN,zh;q=0.9,en;q=0.8",
                "origin": "https://cloud.sambanova.ai",
                "referer": "https://cloud.sambanova.ai/",
                "user-agent": ua.random
            }
            
            response = self.client.get(
                "https://cloud.sambanova.ai/api/config",
                headers=headers
            )
            
            if response.status_code == 200:
                self.config_data = response.json()
                self.client_id = self.config_data.get("clientId")
                self.issuer_base_url = self.config_data.get("issuerBaseUrl")
                self.redirect_url = self.config_data.get("redirectURL")
                
                print(f"✅ 配置获取成功")
                print(f"   Client ID: {self.client_id}")
                print(f"   Issuer: {self.issuer_base_url}")
                print(f"   Redirect: {self.redirect_url}")
                return True
            else:
                print(f"❌ 配置获取失败: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ 配置获取异常: {e}")
            return False
    
    def register_account(self):
        """注册 SambaNova 账户"""
        try:
            print("🚀 开始注册账户...")
            
            # 生成用户信息
            first_name = self.generate_random_string()
            last_name = self.generate_random_string()
            
            print(f"👤 用户信息: {first_name} {last_name}")
            
            # 注册请求
            signup_url = f"https://{self.issuer_base_url}/dbconnections/signup"
            headers = {
                "accept": "*/*",
                "accept-language": "zh-CN,zh;q=0.9,en;q=0.8",
                "origin": "https://cloud.sambanova.ai",
                "referer": "https://cloud.sambanova.ai/",
                "user-agent": UserAgent().random,
                "auth0-client": "eyJuYW1lIjoibG9jay5qcyIsInZlcnNpb24iOiIxMi4zLjAiLCJlbnYiOnsiYXV0aDAuanMiOiI5LjIyLjEifX0=",
                "content-type": "application/json"
            }
            
            data = {
                "client_id": self.client_id,
                "connection": "Username-Password-Authentication",
                "email": self.email_address,
                "password": self.password,
                "user_metadata": {
                    "first_name": first_name,
                    "last_name": last_name,
                    "company": "Tech Corp",
                    "country": "Japan",
                    "job_title": "Engineering Leadership",
                    "opted_in_to_marketing_emails__c": "false"
                }
            }
            
            response = self.client.post(signup_url, headers=headers, json=data)
            
            if response.status_code == 200:
                print("✅ 账户注册成功")
                return True
            else:
                print(f"❌ 注册失败: {response.status_code}")
                print(f"   响应: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ 注册异常: {e}")
            return False
    
    def get_verification_link(self, max_attempts=15, delay=3):
        """获取邮箱验证链接"""
        try:
            print("📬 等待验证邮件...")
            
            for attempt in range(max_attempts):
                print(f"📧 检查邮件 (第 {attempt + 1}/{max_attempts} 次)...")
                
                response = self.client.get(
                    'https://minmail.app/api/mail/list',
                    params={'part': 'main'},
                    headers={
                        'referer': 'https://minmail.app/'
                    }
                )
                
                if response.status_code == 200:
                    data = response.json()
                    messages = data.get('message', [])
                    
                    for msg in messages:
                        from_addr = msg.get('from', '')
                        subject = msg.get('subject', '')
                        content = msg.get('content', '')
                        
                        # 查找 SambaNova 验证邮件
                        if ('sambanova' in from_addr.lower() or 
                            'verify' in subject.lower() or
                            'verification' in subject.lower()):
                            print("🎯 找到验证邮件!")
                            
                            # 从HTML内容中提取验证链接
                            soup = BeautifulSoup(content, 'html.parser')
                            links = soup.find_all('a', href=True)
                            
                            for link in links:
                                href = link['href']
                                if ('verify' in href.lower() or 
                                    'verification' in href.lower() or
                                    'confirm' in href.lower()):
                                    print("🔗 成功提取验证链接")
                                    return href
                
                if attempt < max_attempts - 1:
                    print(f"⏰ 等待 {delay} 秒后重试...")
                    time.sleep(delay)
            
            print("❌ 未能获取到验证链接")
            return None
            
        except Exception as e:
            print(f"❌ 获取验证链接异常: {e}")
            return None
    
    def verify_email(self, verification_link):
        """验证邮箱"""
        try:
            print("🔗 开始验证邮箱...")

            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                'Accept-Language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7'
            }

            response = self.client.get(verification_link, headers=headers, follow_redirects=True)

            print(f"🔍 验证响应状态码: {response.status_code}")
            print(f"🔍 验证最终URL: {response.url}")

            if response.status_code == 200:
                # 检查响应内容是否包含成功标志
                response_text = response.text.lower()
                if ('verified' in response_text or
                    'success' in response_text or
                    'confirmed' in response_text or
                    'thank you' in response_text):
                    print("✅ 邮箱验证成功")

                    # 验证成功后等待更长时间让系统处理
                    print("⏰ 等待验证生效（15秒）...")
                    time.sleep(15)
                    return True
                else:
                    print("⚠️ 验证链接访问成功，但未确认验证状态")
                    # 即使不确定，也等待一段时间
                    print("⏰ 等待系统处理验证（10秒）...")
                    time.sleep(10)
                    return True
            else:
                print(f"❌ 邮箱验证失败: {response.status_code}")
                return False

        except Exception as e:
            print(f"❌ 邮箱验证异常: {e}")
            return False
    
    def login_and_get_token(self, max_attempts=3):
        """登录并获取 Access Token（带重试机制）"""

        for attempt in range(max_attempts):
            try:
                print(f"🔑 开始登录获取 Token (第 {attempt + 1}/{max_attempts} 次)...")

                # 生成状态参数
                state = secrets.token_urlsafe(32)
                nonce = secrets.token_urlsafe(32)

                ua = UserAgent()
                common_headers = {
                    "accept": "*/*",
                    "accept-language": "zh-CN,zh;q=0.9,en;q=0.8",
                    "origin": "https://cloud.sambanova.ai",
                    "referer": "https://cloud.sambanova.ai/",
                    "user-agent": ua.random
                }
            
                # 第一步：认证
                authenticate_url = f"https://{self.issuer_base_url}/co/authenticate"
                data_auth = {
                    "client_id": self.client_id,
                    "username": self.email_address,
                    "password": self.password,
                    "realm": "Username-Password-Authentication",
                    "credential_type": "http://auth0.com/oauth/grant-type/password-realm"
                }

                response_auth = self.client.post(
                    authenticate_url,
                    headers={**common_headers, "content-type": "application/json"},
                    json=data_auth
                )

                if response_auth.status_code != 200:
                    print(f"❌ 认证失败: {response_auth.status_code}")
                    if attempt < max_attempts - 1:
                        print(f"⏰ 等待 10 秒后重试...")
                        time.sleep(10)
                        continue
                    else:
                        return False

                login_ticket = response_auth.json().get("login_ticket")
                print("✅ 认证成功，获取登录票据")
            
                # 第二步：获取授权码
                authorize_url = f"https://{self.issuer_base_url}/authorize"
                params_code = {
                    "client_id": self.client_id,
                    "response_type": "code",
                    "redirect_uri": self.redirect_url,
                    "scope": "openid profile email",
                    "nonce": nonce,
                    "state": state,
                    "login_ticket": login_ticket,
                    "realm": "Username-Password-Authentication"
                }

                response_code = self.client.get(
                    authorize_url,
                    params=params_code,
                    headers=common_headers,
                    follow_redirects=False
                )

                print(f"🔍 授权响应状态码: {response_code.status_code}")

                if response_code.status_code == 302:
                    location = response_code.headers.get("location")
                    print(f"🔍 重定向位置: {location}")

                    parsed = urllib.parse.urlparse(location)
                    query_params = urllib.parse.parse_qs(parsed.query)
                    code = query_params.get("code", [None])[0]
                    returned_state = query_params.get("state", [None])[0]

                    print(f"🔍 提取的授权码: {code}")
                    print(f"🔍 返回的状态: {returned_state}")

                    if code and returned_state == state:
                        print("✅ 获取授权码成功")

                        # 第三步：获取 Access Token
                        self.client.cookies.set("nonce", nonce, domain="cloud.sambanova.ai")
                        callback_url = f"{self.redirect_url}?code={code}&state={state}&nonce={nonce}"

                        print(f"🔍 回调URL: {callback_url}")

                        response_callback = self.client.get(
                            callback_url,
                            headers=common_headers,
                            follow_redirects=True  # 允许重定向
                        )

                        print(f"🔍 回调响应状态码: {response_callback.status_code}")
                        print(f"🔍 回调最终URL: {response_callback.url}")

                        # 从 cookies 中获取 access_token
                        self.access_token = self.client.cookies.get("access_token")

                        if self.access_token:
                            print("✅ 成功获取 Access Token")
                            return True
                        else:
                            print("❌ 未能从 cookies 中获取 Access Token")
                            print(f"🔍 当前 cookies: {dict(self.client.cookies)}")

                            # 尝试从响应中查找 token
                            if 'access_token' in response_callback.text:
                                print("🔍 在响应内容中发现 access_token")
                                # 可以尝试从响应中提取 token

                            return False
                    else:
                        print("❌ 授权码无效或状态不匹配")
                        print(f"   期望状态: {state}")
                        print(f"   返回状态: {returned_state}")

                        # 如果是第一次尝试，等待后重试
                        if attempt < max_attempts - 1:
                            print(f"⏰ 等待 15 秒后重试...")
                            time.sleep(15)
                            continue
                        else:
                            return False
                else:
                    print(f"❌ 授权失败: {response_code.status_code}")
                    print(f"🔍 响应内容: {response_code.text[:200]}...")

                    # 如果是邮箱验证问题，等待后重试
                    if ('verify' in response_code.text.lower() or
                        'email' in response_code.text.lower()) and attempt < max_attempts - 1:
                        print(f"⏰ 邮箱可能还在验证中，等待 20 秒后重试...")
                        time.sleep(20)
                        continue
                    else:
                        return False

            except Exception as e:
                print(f"❌ 登录异常: {e}")
                if attempt < max_attempts - 1:
                    print(f"⏰ 等待 10 秒后重试...")
                    time.sleep(10)
                    continue
                else:
                    return False

        return False

    def save_token(self):
        """保存 Access Token 到文件"""
        try:
            # 计算过期时间（7天后）
            expire_time = datetime.now() + timedelta(days=7)

            token_info = {
                "email": self.email_address,
                "password": self.password,
                "access_token": self.access_token,
                "created_time": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                "expire_time": expire_time.strftime('%Y-%m-%d %H:%M:%S'),
                "status": "active"
            }

            # 读取现有数据
            try:
                with open(self.keys_file, 'r', encoding='utf-8') as f:
                    existing_data = json.load(f)
            except (FileNotFoundError, json.JSONDecodeError):
                existing_data = []

            # 添加新数据
            existing_data.append(token_info)

            # 保存到文件
            with open(self.keys_file, 'w', encoding='utf-8') as f:
                json.dump(existing_data, f, ensure_ascii=False, indent=2)

            print(f"✅ Token 已保存到 {self.keys_file}")
            return True

        except Exception as e:
            print(f"❌ 保存 Token 异常: {e}")
            return False

    def register_single_account(self):
        """注册单个账户的完整流程"""
        try:
            print(f"\n🎯 开始注册新账户 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

            # 1. 获取邮箱
            if not self.get_email_address():
                return False

            # 2. 生成密码
            self.password = self.generate_password()
            print(f"🔐 生成密码: {self.password}")

            # 3. 获取配置
            if not self.get_sambanova_config():
                return False

            # 4. 注册账户
            if not self.register_account():
                return False

            # 5. 获取验证链接
            verification_link = self.get_verification_link()
            if not verification_link:
                return False

            # 6. 验证邮箱
            if not self.verify_email(verification_link):
                return False

            # 7. 登录获取 Token
            if not self.login_and_get_token():
                return False

            # 8. 保存 Token
            self.save_token()

            # 9. 显示成功信息
            print("\n🎉 账户注册完成!")
            print(f"📧 邮箱: {self.email_address}")
            print(f"🔐 密码: {self.password}")
            print(f"🔑 Access Token: {self.access_token[:20]}...{self.access_token[-10:] if len(self.access_token) > 30 else self.access_token}")
            print(f"⏰ 有效期: 7天")

            return True

        except Exception as e:
            print(f"❌ 注册流程异常: {e}")
            return False

def show_logo():
    """显示程序Logo"""
    logo = """
╔══════════════════════════════════════════════════════════════╗
║                                                              ║
║   ███████╗ █████╗ ███╗   ███╗██████╗  █████╗ ███╗   ██╗     ║
║   ██╔════╝██╔══██╗████╗ ████║██╔══██╗██╔══██╗████╗  ██║     ║
║   ███████╗███████║██╔████╔██║██████╔╝███████║██╔██╗ ██║     ║
║   ╚════██║██╔══██║██║╚██╔╝██║██╔══██╗██╔══██║██║╚██╗██║     ║
║   ███████║██║  ██║██║ ╚═╝ ██║██████╔╝██║  ██║██║ ╚████║     ║
║   ╚══════╝╚═╝  ╚═╝╚═╝     ╚═╝╚═════╝ ╚═╝  ╚═╝╚═╝  ╚═══╝     ║
║                                                              ║
║                🤖 SambaNova AI 自动注册机 🤖                ║
║             💫 完整自动化，轻松获取 API Key 💫              ║
║                                                              ║
╚══════════════════════════════════════════════════════════════╝
"""
    print(logo)

def main():
    """主程序"""
    show_logo()

    # 显示程序信息
    print("🔧 程序版本：v1.0.0")
    print("👨‍💻 作者信息：云胡不喜@linux.do")
    print("📧 临时邮箱：minmail.app")
    print("🎯 目标平台：SambaNova AI")
    print("⚡ 特色功能：自动注册、邮箱验证、Token获取、7天有效期")

    while True:
        # 获取用户选择
        print("\n📋 请选择运行模式：")
        print("   1️⃣  单个账户注册")
        print("   2️⃣  批量账户注册")
        print("   3️⃣  查看已保存的 Token")
        print("   0️⃣  退出程序")
        print("=" * 50)

        choice = input("请输入选择 (0/1/2/3): ").strip()

        if choice == '0':
            print("👋 程序结束，感谢使用！")
            break
        elif choice == '1':
            print("\n🎯 启动单个账户注册模式...")
            sambanova = SambaNovaAuto()
            success = sambanova.register_single_account()

            if success:
                print("\n✅ 注册成功！")
            else:
                print("\n❌ 注册失败！")

            # 询问是否继续
            continue_choice = input("\n🤔 是否继续注册？(y/n): ").strip().lower()
            if continue_choice not in ['y', 'yes', '是']:
                break

        elif choice == '2':
            print("\n🎯 启动批量账户注册模式...")
            try:
                count = int(input("请输入要注册的账户数量: "))
                if count <= 0:
                    print("❌ 数量必须大于0")
                    continue

                success_count = 0
                for i in range(count):
                    print(f"\n{'='*20} 第 {i+1}/{count} 个账户 {'='*20}")
                    sambanova = SambaNovaAuto()
                    if sambanova.register_single_account():
                        success_count += 1

                    # 批量注册间隔
                    if i < count - 1:
                        print("⏰ 等待 5 秒后继续...")
                        time.sleep(5)

                print(f"\n🎉 批量注册完成！成功: {success_count}/{count}")

            except ValueError:
                print("❌ 请输入有效的数字")

        elif choice == '3':
            print("\n📋 查看已保存的 Token...")
            try:
                with open('sambanova_keys.txt', 'r', encoding='utf-8') as f:
                    tokens = json.load(f)

                if not tokens:
                    print("📭 暂无保存的 Token")
                else:
                    print(f"📊 共找到 {len(tokens)} 个 Token:")
                    for i, token in enumerate(tokens, 1):
                        print(f"\n{i}. 邮箱: {token['email']}")
                        print(f"   Token: {token['access_token'][:20]}...{token['access_token'][-10:]}")
                        print(f"   创建时间: {token['created_time']}")
                        print(f"   过期时间: {token['expire_time']}")
                        print(f"   状态: {token['status']}")

            except (FileNotFoundError, json.JSONDecodeError):
                print("📭 暂无保存的 Token")
        else:
            print("❌ 无效选择，请重新输入")

if __name__ == "__main__":
    main()
