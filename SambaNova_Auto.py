#!/usr/bin/env python3
"""
SambaNova AI 自动注册程序
完整自动化方案：注册 → 邮箱验证 → 登录获取 API Key
"""

import json
import time
import random
import string
import secrets
import urllib.parse
import httpx
from bs4 import BeautifulSoup
from fake_useragent import UserAgent
from datetime import datetime, timed<PERSON>ta
from simple_proxy_pool import SimpleProxyPool

class SambaNovaAuto:
    def __init__(self, use_proxy=False):
        self.email_address = None
        self.password = None
        self.access_token = None
        self.keys_file = "sambanova_keys.txt"
        # self.use_proxy = use_proxy  # 注释掉代理功能
        # self.proxy_pool = None
        # self.current_proxy = None

        # 暂时注释掉代理池功能
        # if use_proxy:
        #     print("🌐 初始化代理池...")
        #     self.proxy_pool = SimpleProxyPool()
        #     if self.proxy_pool.refresh_proxy_pool(max_test_proxies=5):
        #         self.current_proxy = self.proxy_pool.get_working_proxy()
        #         print(f"✅ 代理池初始化成功，当前代理: {self.current_proxy}")
        #     else:
        #         print("⚠️ 代理池初始化失败，将使用直连")
        #         self.use_proxy = False

        # HTTP客户端配置 - 只使用直连
        self.client = httpx.Client(
            timeout=60.0,
            headers={
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'Accept': '*/*',
                'Accept-Language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7'
            }
        )

        # SambaNova 配置
        self.config_data = None
        self.client_id = None
        self.issuer_base_url = None
        self.redirect_url = None
    
    def generate_random_string(self, length=5):
        """生成随机字符串"""
        letters = string.ascii_lowercase
        return ''.join(random.choice(letters) for _ in range(length))
    
    def generate_password(self):
        """生成符合要求的随机密码"""
        # SambaNova 要求：至少8位，包含至少3种字符类型
        length = 12

        # 定义字符类型
        lowercase = string.ascii_lowercase
        uppercase = string.ascii_uppercase
        digits = string.digits
        special = "!@#$%^&*"

        # 确保包含至少3种字符类型
        password = []

        # 每种类型至少包含一个字符
        password.append(random.choice(lowercase))
        password.append(random.choice(uppercase))
        password.append(random.choice(digits))
        password.append(random.choice(special))

        # 填充剩余长度
        all_chars = lowercase + uppercase + digits + special
        for _ in range(length - 4):
            password.append(random.choice(all_chars))

        # 打乱顺序
        random.shuffle(password)

        return ''.join(password)

    # 注释掉代理切换功能
    # def switch_proxy(self):
    #     """切换到新的代理"""
    #     if not self.use_proxy or not self.proxy_pool:
    #         return False
    #
    #     # 标记当前代理为失败
    #     if self.current_proxy:
    #         self.proxy_pool.mark_proxy_failed(self.current_proxy)
    #         print(f"❌ 标记代理失败: {self.current_proxy}")
    #
    #     # 获取新代理
    #     new_proxy = self.proxy_pool.get_working_proxy()
    #     if new_proxy:
    #         self.current_proxy = new_proxy
    #
    #         # 重新创建客户端
    #         client_kwargs = {
    #             'timeout': 60.0,
    #             'headers': {
    #                 'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    #                 'Accept': '*/*',
    #                 'Accept-Language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7'
    #             },
    #             'proxy': f"http://{new_proxy}"
    #         }
    #
    #         self.client.close()
    #         self.client = httpx.Client(**client_kwargs)
    #
    #         print(f"🔄 切换到新代理: {new_proxy}")
    #         return True
    #     else:
    #         print("❌ 没有可用的代理了")
    #         return False

    def test_network_connection(self):
        """测试网络连接"""
        try:
            print("🌐 测试网络连接...")
            response = self.client.get('https://httpbin.org/get', timeout=10)
            if response.status_code == 200:
                print("✅ 网络连接正常")
                return True
            else:
                print(f"⚠️ 网络连接异常: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 网络连接失败: {e}")
            return False

    def get_email_address(self):
        """获取临时邮箱地址（使用 minmail.app）- 增强版本"""
        print("📧 获取临时邮箱地址...")

        # 多个临时邮箱服务备选
        email_services = [
            {
                'name': 'minmail.app',
                'url': 'https://minmail.app/api/mail/address',
                'params': {'refresh': 'true', 'expire': '1440', 'part': 'main'},
                'headers': {'referer': 'https://minmail.app/'},
                'address_key': 'address'
            }
        ]

        for service in email_services:
            print(f"🔄 尝试服务: {service['name']}")

            # 每个服务尝试3次
            for attempt in range(3):
                try:
                    print(f"   第 {attempt + 1}/3 次尝试...")

                    response = self.client.get(
                        service['url'],
                        params=service['params'],
                        headers=service['headers'],
                        timeout=15  # 减少超时时间
                    )

                    if response.status_code == 200:
                        data = response.json()
                        email_address = data.get(service['address_key'])

                        if email_address:
                            self.email_address = email_address
                            print(f"✅ 获取邮箱成功: {self.email_address}")
                            return True
                        else:
                            print("⚠️ 响应中未找到邮箱地址")
                    else:
                        print(f"⚠️ HTTP错误: {response.status_code}")

                except Exception as e:
                    print(f"⚠️ 请求异常: {str(e)[:100]}...")

                if attempt < 2:  # 不是最后一次尝试
                    print("   等待 3 秒后重试...")
                    time.sleep(3)

        # 如果所有服务都失败，生成一个随机邮箱作为备用
        print("⚠️ 所有邮箱服务都失败，生成随机邮箱...")
        random_name = ''.join(random.choices(string.ascii_lowercase, k=8))
        random_number = ''.join(random.choices(string.digits, k=3))

        # 使用一些常见的临时邮箱域名
        temp_domains = [
            'atminmail.com', 'guerrillamail.com', '10minutemail.com',
            'tempmail.org', 'mailinator.com'
        ]

        domain = random.choice(temp_domains)
        self.email_address = f"{random_name}{random_number}@{domain}"

        print(f"💡 生成随机邮箱: {self.email_address}")
        print("⚠️ 注意：此邮箱可能无法接收邮件，建议检查网络连接")

        return True
    
    def get_sambanova_config(self):
        """获取 SambaNova 配置信息"""
        try:
            print("🔧 获取 SambaNova 配置...")
            
            ua = UserAgent()
            headers = {
                "accept": "*/*",
                "accept-language": "zh-CN,zh;q=0.9,en;q=0.8",
                "origin": "https://cloud.sambanova.ai",
                "referer": "https://cloud.sambanova.ai/",
                "user-agent": ua.random
            }
            
            response = self.client.get(
                "https://cloud.sambanova.ai/api/config",
                headers=headers
            )
            
            if response.status_code == 200:
                self.config_data = response.json()
                self.client_id = self.config_data.get("clientId")
                self.issuer_base_url = self.config_data.get("issuerBaseUrl")
                self.redirect_url = self.config_data.get("redirectURL")
                
                print(f"✅ 配置获取成功")
                print(f"   Client ID: {self.client_id}")
                print(f"   Issuer: {self.issuer_base_url}")
                print(f"   Redirect: {self.redirect_url}")
                return True
            else:
                print(f"❌ 配置获取失败: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ 配置获取异常: {e}")
            return False
    
    def register_account(self):
        """注册 SambaNova 账户"""
        try:
            print("🚀 开始注册账户...")
            
            # 生成用户信息
            first_name = self.generate_random_string()
            last_name = self.generate_random_string()
            
            print(f"👤 用户信息: {first_name} {last_name}")
            
            # 注册请求
            signup_url = f"https://{self.issuer_base_url}/dbconnections/signup"
            headers = {
                "accept": "*/*",
                "accept-language": "zh-CN,zh;q=0.9,en;q=0.8",
                "origin": "https://cloud.sambanova.ai",
                "referer": "https://cloud.sambanova.ai/",
                "user-agent": UserAgent().random,
                "auth0-client": "eyJuYW1lIjoibG9jay5qcyIsInZlcnNpb24iOiIxMi4zLjAiLCJlbnYiOnsiYXV0aDAuanMiOiI5LjIyLjEifX0=",
                "content-type": "application/json"
            }
            
            data = {
                "client_id": self.client_id,
                "connection": "Username-Password-Authentication",
                "email": self.email_address,
                "password": self.password,
                "user_metadata": {
                    "first_name": first_name,
                    "last_name": last_name,
                    "company": "Tech Corp",
                    "country": "Japan",
                    "job_title": "Engineering Leadership",
                    "opted_in_to_marketing_emails__c": "false"
                }
            }
            
            response = self.client.post(signup_url, headers=headers, json=data)
            
            if response.status_code == 200:
                print("✅ 账户注册成功")
                return True
            else:
                print(f"❌ 注册失败: {response.status_code}")
                print(f"   响应: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ 注册异常: {e}")
            return False
    
    def get_verification_link(self, max_attempts=15, delay=3):
        """获取邮箱验证链接"""
        try:
            print("📬 等待验证邮件...")
            
            for attempt in range(max_attempts):
                print(f"📧 检查邮件 (第 {attempt + 1}/{max_attempts} 次)...")
                
                response = self.client.get(
                    'https://minmail.app/api/mail/list',
                    params={'part': 'main'},
                    headers={
                        'referer': 'https://minmail.app/'
                    }
                )
                
                if response.status_code == 200:
                    data = response.json()
                    messages = data.get('message', [])
                    
                    for msg in messages:
                        from_addr = msg.get('from', '')
                        subject = msg.get('subject', '')
                        content = msg.get('content', '')
                        
                        # 查找 SambaNova 验证邮件
                        if ('sambanova' in from_addr.lower() or
                            'verify' in subject.lower() or
                            'verification' in subject.lower() or
                            'welcome' in subject.lower()):
                            print("🎯 找到验证邮件!")
                            print(f"📧 发件人: {from_addr}")
                            print(f"📧 主题: {subject}")

                            # 从HTML内容中提取验证链接
                            soup = BeautifulSoup(content, 'html.parser')
                            links = soup.find_all('a', href=True)

                            for link in links:
                                href = link['href']
                                link_text = link.get_text().strip().lower()

                                # 更全面的验证链接检测
                                if (('verify' in href.lower() or
                                     'verification' in href.lower() or
                                     'confirm' in href.lower() or
                                     'email-verification' in href.lower()) and
                                    ('auth0.sambanova.ai' in href or 'sambanova' in href)):
                                    print("🔗 成功提取验证链接")
                                    print(f"🔗 链接: {href}")
                                    return href

                                # 也检查链接文本
                                if ('verify' in link_text or
                                    'confirm' in link_text or
                                    'activate' in link_text):
                                    print("🔗 通过链接文本找到验证链接")
                                    print(f"🔗 链接: {href}")
                                    return href
                
                if attempt < max_attempts - 1:
                    print(f"⏰ 等待 {delay} 秒后重试...")
                    time.sleep(delay)
            
            print("❌ 未能获取到验证链接")
            return None
            
        except Exception as e:
            print(f"❌ 获取验证链接异常: {e}")
            return None
    
    def verify_email(self, verification_link):
        """使用 Playwright 在真实浏览器上下文完成邮箱验证"""
        try:
            from playwright.sync_api import sync_playwright, TimeoutError
        except ImportError:
            print("❌ Playwright 未安装，无法进行可靠邮箱验证")
            print("💡 请执行: pip install playwright && playwright install")
            return False

        try:
            print("🔗 开始邮箱验证（浏览器上下文）...")
            print(f"🔗 验证链接: {verification_link}")

            with sync_playwright() as p:
                browser = p.chromium.launch(headless=True)
                context = browser.new_context(
                    user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
                )
                page = context.new_page()

                try:
                    resp = page.goto(verification_link, wait_until='networkidle', timeout=60000)
                    if not resp or resp.status != 200:
                         print(f"❌ 无法加载验证页面，状态: {resp.status if resp else 'N/A'}")
                         browser.close()
                         return False

                    page.wait_for_timeout(5000) # 等待JS执行和重定向

                    current_url = page.url.lower()
                    page_content = page.content().lower()

                    # 增强成功标志检测
                    text_success_indicators = [
                        'email verified', # "email verified. please login to continue."
                        'verification successful',
                        'account verified',
                        'continue using the application',
                        'successfully verified',
                    ]
                    
                    if any(indicator in page_content for indicator in text_success_indicators):
                         print("✅ 邮箱验证成功（内容检测）")
                         browser.close()
                         return True
                    
                    # URL跳转也可能是成功的标志
                    if 'dashboard' in current_url or 'login' in current_url:
                        print("✅ 邮箱验证成功（URL跳转）")
                        browser.close()
                        return True

                    print("❌ 验证失败，未找到成功标志")
                    browser.close()
                    return False

                except TimeoutError:
                    print("❌ 验证页面加载超时")
                    browser.close()
                    return False
                except Exception as e:
                    print(f"❌ 页面操作异常: {e}")
                    browser.close()
                    return False
        except Exception as e:
            print(f"❌ 浏览器验证异常: {e}")
            import traceback
            print(f"🔍 详细错误: {traceback.format_exc()}")
            return False

    def login_and_get_token(self):
        """登录并获取 Access Token"""
        try:
            print("🔑 开始登录获取 Token...")

            # 生成状态参数
            state = secrets.token_urlsafe(32)
            nonce = secrets.token_urlsafe(32)

            ua = UserAgent()
            common_headers = {
                "accept": "*/*",
                "accept-language": "zh-CN,zh;q=0.9,en;q=0.8",
                "origin": "https://cloud.sambanova.ai",
                "referer": "https://cloud.sambanova.ai/",
                "user-agent": ua.random
            }

            # 第一步：认证
            authenticate_url = f"https://{self.issuer_base_url}/co/authenticate"
            data_auth = {
                "client_id": self.client_id,
                "username": self.email_address,
                "password": self.password,
                "realm": "Username-Password-Authentication",
                "credential_type": "http://auth0.com/oauth/grant-type/password-realm"
            }

            response_auth = self.client.post(
                authenticate_url,
                headers={**common_headers, "content-type": "application/json"},
                json=data_auth
            )

            if response_auth.status_code != 200:
                print(f"❌ 认证失败: {response_auth.status_code}")
                return False

            login_ticket = response_auth.json().get("login_ticket")
            print("✅ 认证成功，获取登录票据")

            # 第二步：获取授权码
            authorize_url = f"https://{self.issuer_base_url}/authorize"
            params_code = {
                "client_id": self.client_id,
                "response_type": "code",
                "redirect_uri": self.redirect_url,
                "scope": "openid profile email",
                "nonce": nonce,
                "state": state,
                "login_ticket": login_ticket,
                "realm": "Username-Password-Authentication"
            }

            response_code = self.client.get(
                authorize_url,
                params=params_code,
                headers=common_headers,
                follow_redirects=False
            )

            print(f"🔍 授权响应状态码: {response_code.status_code}")

            if response_code.status_code == 302:
                    location = response_code.headers.get("location")
                    print(f"🔍 重定向位置: {location}")

                    # 解析重定向URL
                    parsed = urllib.parse.urlparse(location)
                    query_params = urllib.parse.parse_qs(parsed.query)

                    # 检查是否有错误信息
                    error = query_params.get("error", [None])[0]
                    error_description = query_params.get("error_description", [None])[0]

                    if error:
                        print(f"❌ 授权错误: {error}")
                        print(f"❌ 错误描述: {error_description}")

                        # 如果是邮箱验证问题
                        if error == 'access_denied' and error_description and 'verify' in error_description.lower():
                            print("❌ 邮箱验证问题：验证链接没有正确处理")
                            print("💡 建议：检查邮箱验证链接的访问方式")
                            return False
                        else:
                            print(f"❌ 其他授权错误: {error}")
                            return False

                    # 如果没有错误，提取授权码
                    code = query_params.get("code", [None])[0]
                    returned_state = query_params.get("state", [None])[0]

                    print(f"🔍 提取的授权码: {code}")
                    print(f"🔍 返回的状态: {returned_state}")

                    if code and returned_state == state:
                        print("✅ 获取授权码成功")

                        # 第三步：获取 Access Token
                        self.client.cookies.set("nonce", nonce, domain="cloud.sambanova.ai")
                        callback_url = f"{self.redirect_url}?code={code}&state={state}&nonce={nonce}"

                        print(f"🔍 回调URL: {callback_url}")

                        response_callback = self.client.get(
                            callback_url,
                            headers=common_headers,
                            follow_redirects=True  # 允许重定向
                        )

                        print(f"🔍 回调响应状态码: {response_callback.status_code}")
                        print(f"🔍 回调最终URL: {response_callback.url}")

                        # 从 cookies 中获取 access_token
                        self.access_token = self.client.cookies.get("access_token")

                        if self.access_token:
                            print("✅ 成功获取 Access Token")
                            return True
                        else:
                            print("❌ 未能从 cookies 中获取 Access Token")
                            print(f"🔍 当前 cookies: {dict(self.client.cookies)}")

                            # 尝试从响应中查找 token
                            if 'access_token' in response_callback.text:
                                print("🔍 在响应内容中发现 access_token")
                                # 可以尝试从响应中提取 token

                            return False
                    else:
                        print("❌ 授权码无效或状态不匹配")
                        print(f"   期望状态: {state}")
                        print(f"   返回状态: {returned_state}")
                        return False
            else:
                print(f"❌ 授权失败: {response_code.status_code}")
                print(f"🔍 响应内容: {response_code.text[:200]}...")
                return False

        except Exception as e:
            print(f"❌ 登录异常: {e}")
            return False

    def create_api_key(self, key_name="Auto Generated Key"):
        """通过多种方式创建API Key"""
        try:
            print("🔑 正在创建 API Key...")

            if not self.access_token:
                print("❌ 缺少 Access Token，无法创建 API Key")
                return None

            # 使用通过浏览器分析得到的正确API端点
            api_url = "https://cloud.sambanova.ai/api/keys"

            # 使用session cookies而不是Bearer Token
            headers = {
                "Content-Type": "application/json",
                "Accept": "application/json",
                "Origin": "https://cloud.sambanova.ai",
                "Referer": "https://cloud.sambanova.ai/apis",
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
            }

            # 从邮箱地址提取用户ID
            user_id = self.email_address if self.email_address else "unknown"

            data = {
                "name": key_name,
                "description": "Auto generated API key",
                "userId": user_id,
                "userName": user_id
            }

            print(f"🔍 使用正确的API端点: {api_url}")

            try:
                response = self.client.post(api_url, headers=headers, json=data, timeout=15)

                print(f"🔍 响应状态码: {response.status_code}")

                if response.status_code in [200, 201]:
                    result = response.json()

                    # 尝试多种可能的key字段名
                    api_key = (result.get("apiKey") or  # SambaNova使用这个字段
                             result.get("key") or
                             result.get("api_key") or
                             result.get("token") or
                             result.get("id") or
                             result.get("secret"))

                    if api_key:
                        print("✅ API Key 创建成功")
                        print(f"🔑 API Key: {api_key}")
                        return api_key
                    else:
                        print("⚠️ 响应成功但未找到 API Key")
                        print(f"🔍 响应内容: {result}")
                        return None
                else:
                    print(f"❌ API Key 创建失败: {response.status_code}")
                    print(f"🔍 错误内容: {response.text[:200]}...")
                    return None

            except Exception as api_error:
                print(f"❌ API请求异常: {api_error}")
                return None



        except Exception as e:
            print(f"❌ 创建 API Key 异常: {e}")
            return None

    def save_api_key(self, api_key):
        """保存 API Key 到文件"""
        try:
            # 计算过期时间（30天后，API Key 通常有效期更长）
            expire_time = datetime.now() + timedelta(days=30)

            key_info = {
                "email": self.email_address,
                "password": self.password,
                "api_key": api_key,
                "access_token": self.access_token,
                "created_time": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                "expire_time": expire_time.strftime('%Y-%m-%d %H:%M:%S'),
                "status": "active",
                "type": "api_key"
            }

            # 读取现有数据
            try:
                with open(self.keys_file, 'r', encoding='utf-8') as f:
                    existing_data = json.load(f)
            except (FileNotFoundError, json.JSONDecodeError):
                existing_data = []

            # 添加新数据
            existing_data.append(key_info)

            # 保存到文件
            with open(self.keys_file, 'w', encoding='utf-8') as f:
                json.dump(existing_data, f, ensure_ascii=False, indent=2)

            print(f"✅ API Key 已保存到 {self.keys_file}")
            return True

        except Exception as e:
            print(f"❌ 保存 API Key 异常: {e}")
            return False

    def save_api_key_simple(self, api_key):
        """简化保存API Key到文件"""
        try:
            # 只保存API Key，格式简化
            key_info = f"{api_key}\n"

            # 追加到文件
            with open(self.keys_file, 'a', encoding='utf-8') as f:
                f.write(key_info)

            print(f"✅ API Key 已保存到 {self.keys_file}")
            return True

        except Exception as e:
            print(f"❌ 保存 API Key 异常: {e}")
            return False

    def save_token(self):
        """保存 Access Token 到文件"""
        try:
            # 计算过期时间（7天后）
            expire_time = datetime.now() + timedelta(days=7)

            token_info = {
                "email": self.email_address,
                "password": self.password,
                "access_token": self.access_token,
                "created_time": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                "expire_time": expire_time.strftime('%Y-%m-%d %H:%M:%S'),
                "status": "active"
            }

            # 读取现有数据
            try:
                with open(self.keys_file, 'r', encoding='utf-8') as f:
                    existing_data = json.load(f)
            except (FileNotFoundError, json.JSONDecodeError):
                existing_data = []

            # 添加新数据
            existing_data.append(token_info)

            # 保存到文件
            with open(self.keys_file, 'w', encoding='utf-8') as f:
                json.dump(existing_data, f, ensure_ascii=False, indent=2)

            print(f"✅ Token 已保存到 {self.keys_file}")
            return True

        except Exception as e:
            print(f"❌ 保存 Token 异常: {e}")
            return False

    def register_single_account(self):
        """注册单个账户的完整流程"""
        try:
            print(f"\n🎯 开始注册新账户 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

            # 1. 获取邮箱
            if not self.get_email_address():
                return False

            # 2. 生成密码
            self.password = self.generate_password()
            print(f"🔐 生成密码: {self.password}")

            # 3. 获取配置
            if not self.get_sambanova_config():
                return False

            # 4. 注册账户
            if not self.register_account():
                return False

            # 5. 获取验证链接（增加重试机制）
            verification_link = None
            for retry in range(3):
                verification_link = self.get_verification_link()
                if verification_link:
                    break
                if retry < 2:
                    print(f"⏰ 验证链接获取失败，等待 30 秒后重试 ({retry + 1}/3)...")
                    time.sleep(30)

            if not verification_link:
                print("❌ 多次尝试后仍无法获取验证链接")
                return False

            # 6. 验证邮箱（使用Playwright进行浏览器验证）
            if not self.verify_email(verification_link):
                 print("❌ 邮箱验证失败")
                 return False

            # 7. 登录获取 Token（智能重试策略）
            if not self.login_and_get_token():
                print("⚠️ 首次登录失败，可能需要更多时间同步验证状态")
                print("⏰ 等待额外15秒后重试...")
                time.sleep(15)

                if not self.login_and_get_token():
                    print("❌ 登录失败，邮箱验证可能未成功")
                    return False
                else:
                    print("✅ 延迟重试登录成功")

            # 8. 创建 API Key
            api_key = self.create_api_key("Auto Generated Key")

            if api_key:
                # 成功创建真正的 API Key
                self.save_api_key_simple(api_key)
                print(f"\n🎉 API Key 创建成功!")
                print(f"🔑 {api_key}")
            else:
                print("❌ API Key 创建失败")
                return False

            return True

        except Exception as e:
            print(f"❌ 注册流程异常: {e}")
            return False

def show_logo():
    """显示程序Logo"""
    logo = """
╔══════════════════════════════════════════════════════════════╗
║                                                              ║
║   ███████╗ █████╗ ███╗   ███╗██████╗  █████╗ ███╗   ██╗     ║
║   ██╔════╝██╔══██╗████╗ ████║██╔══██╗██╔══██╗████╗  ██║     ║
║   ███████╗███████║██╔████╔██║██████╔╝███████║██╔██╗ ██║     ║
║   ╚════██║██╔══██║██║╚██╔╝██║██╔══██╗██╔══██║██║╚██╗██║     ║
║   ███████║██║  ██║██║ ╚═╝ ██║██████╔╝██║  ██║██║ ╚████║     ║
║   ╚══════╝╚═╝  ╚═╝╚═╝     ╚═╝╚═════╝ ╚═╝  ╚═╝╚═╝  ╚═══╝     ║
║                                                              ║
║                🤖 SambaNova AI 自动注册机 🤖                ║
║             💫 完整自动化，轻松获取 API Key 💫              ║
║                                                              ║
╚══════════════════════════════════════════════════════════════╝
"""
    print(logo)

def main():
    """主程序"""
    show_logo()

    # 显示程序信息
    print("🔧 程序版本：v1.0.0")
    print("👨‍💻 作者信息：云胡不喜@linux.do")
    print("📧 临时邮箱：minmail.app")
    print("🎯 目标平台：SambaNova AI")
    print("⚡ 特色功能：自动注册、邮箱验证、Token获取、7天有效期")

    while True:
        # 获取用户选择
        print("\n📋 请选择运行模式：")
        print("   1️⃣  单个账户注册")
        # print("   2️⃣  批量账户注册")  # 暂时注释掉
        print("   2️⃣  查看已保存的 API Keys")
        print("   0️⃣  退出程序")
        print("=" * 50)

        choice = input("请输入选择 (0/1/2): ").strip()

        if choice == '0':
            print("👋 程序结束，感谢使用！")
            break
        elif choice == '1':
            print("\n🎯 启动单个账户注册模式...")
            sambanova = SambaNovaAuto()
            success = sambanova.register_single_account()

            if success:
                print("\n✅ 注册成功！")
            else:
                print("\n❌ 注册失败！")

            # 询问是否继续
            continue_choice = input("\n🤔 是否继续注册？(y/n): ").strip().lower()
            if continue_choice not in ['y', 'yes', '是']:
                break

        elif choice == '2':
            print("\n🎯 启动批量账户注册模式...")
            try:
                count = int(input("请输入要注册的账户数量: "))
                if count <= 0:
                    print("❌ 数量必须大于0")
                    continue

                success_count = 0
                sambanova = SambaNovaAuto()

                for i in range(count):
                    print(f"\n{'='*20} 第 {i+1}/{count} 个账户 {'='*20}")

                    # 简单重试机制
                    retry_count = 0
                    max_retries = 2

                    while retry_count < max_retries:
                        if sambanova.register_single_account():
                            success_count += 1
                            print(f"✅ 第 {i+1} 个账户注册成功")
                            break
                        else:
                            retry_count += 1
                            if retry_count < max_retries:
                                print(f"⚠️ 注册失败，第 {retry_count}/{max_retries} 次重试...")
                                time.sleep(3)
                            else:
                                print(f"❌ 第 {i+1} 个账户注册失败（已重试{max_retries}次）")

                    # 批量注册间隔
                    if i < count - 1:
                        print("⏰ 等待 10 秒后继续...")
                        time.sleep(10)

                print(f"\n🎉 批量注册完成！成功: {success_count}/{count}")

            except ValueError:
                print("❌ 请输入有效的数字")
                
        elif choice == '3':
            print("\n📋 查看已保存的 API Keys...")
            try:
                with open('sambanova_keys.txt', 'r', encoding='utf-8') as f:
                    content = f.read().strip()

                if not content:
                    print("📭 暂无保存的 API Keys")
                else:
                    keys = content.split('\n')
                    keys = [key.strip() for key in keys if key.strip()]
                    print(f"📊 共找到 {len(keys)} 个 API Keys:")
                    for i, key in enumerate(keys, 1):
                        print(f"  {i}. {key}")

            except (FileNotFoundError, json.JSONDecodeError):
                print("📭 暂无保存的 Token")
        else:
            print("❌ 无效选择，请重新输入")

def test_password_generation():
    """测试密码生成功能"""
    print("🧪 测试密码生成功能...")
    sambanova = SambaNovaAuto()

    for i in range(5):
        password = sambanova.generate_password()
        print(f"密码 {i+1}: {password}")

        # 检查密码强度
        has_lower = any(c.islower() for c in password)
        has_upper = any(c.isupper() for c in password)
        has_digit = any(c.isdigit() for c in password)
        has_special = any(c in "!@#$%^&*" for c in password)

        types_count = sum([has_lower, has_upper, has_digit, has_special])
        print(f"  长度: {len(password)}, 字符类型: {types_count}/4")
        print(f"  小写: {has_lower}, 大写: {has_upper}, 数字: {has_digit}, 特殊: {has_special}")

        if len(password) >= 8 and types_count >= 3:
            print("  ✅ 符合要求")
        else:
            print("  ❌ 不符合要求")
        print()

def test_verification_flow():
    """测试验证流程"""
    print("🧪 测试邮箱验证流程...")
    sambanova = SambaNovaAuto()

    # 模拟验证链接
    test_link = "https://auth0.sambanova.ai/u/email-verification?ticket=test123"

    print(f"🔗 测试验证链接: {test_link}")
    result = sambanova.verify_email(test_link)

    if result:
        print("✅ 验证流程测试通过")
    else:
        print("❌ 验证流程测试失败")

    return result

def test_single_registration():
    """测试单个账户注册流程"""
    print("🧪 开始测试单个账户注册流程...")
    sambanova = SambaNovaAuto()

    try:
        success = sambanova.register_single_account()
        if success:
            print("✅ 测试成功：账户注册完成")
            return True
        else:
            print("❌ 测试失败：账户注册失败")
            return False
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

if __name__ == "__main__":
    # 添加测试选项
    import sys
    if len(sys.argv) > 1:
        if sys.argv[1] == "test":
            test_single_registration()
        elif sys.argv[1] == "test-password":
            test_password_generation()
        elif sys.argv[1] == "test-verify":
            test_verification_flow()
        else:
            print("可用选项: test, test-password, test-verify")
    else:
        main()
