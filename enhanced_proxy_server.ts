import { serve } from "https://deno.land/std@0.170.0/http/server.ts";

// CORS headers
const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
  "Access-Control-Allow-Headers": "Content-Type, Authorization",
};

// 多token配置
interface TokenConfig {
  token: string;
  endpoint: string;
  model: string;
  weight: number;  // 权重，用于负载均衡
  active: boolean; // 是否可用
  lastUsed: number; // 最后使用时间
  errorCount: number; // 错误计数
}

// 从环境变量读取配置
const getTokenConfigs = (): TokenConfig[] => {
  const configs: TokenConfig[] = [];
  
  // 支持环境变量配置
  const tokenList = Deno.env.get("API_TOKENS")?.split(",") || [];
  const endpointList = Deno.env.get("API_ENDPOINTS")?.split(",") || [];
  const modelList = Deno.env.get("API_MODELS")?.split(",") || [];
  
  // 如果没有环境变量，使用默认配置
  if (tokenList.length === 0) {
    return [
      {
        token: "default-token",
        endpoint: "https://chat.z.ai/api/chat/completions",
        model: "0727-360B-API",
        weight: 1,
        active: true,
        lastUsed: 0,
        errorCount: 0
      }
    ];
  }
  
  // 从环境变量构建配置
  for (let i = 0; i < tokenList.length; i++) {
    configs.push({
      token: tokenList[i].trim(),
      endpoint: endpointList[i]?.trim() || "https://chat.z.ai/api/chat/completions",
      model: modelList[i]?.trim() || "0727-360B-API",
      weight: 1,
      active: true,
      lastUsed: 0,
      errorCount: 0
    });
  }
  
  return configs;
};

// Token管理器
class TokenManager {
  private configs: TokenConfig[];
  private currentIndex: number = 0;
  
  constructor() {
    this.configs = getTokenConfigs();
    console.log(`Loaded ${this.configs.length} token configurations`);
  }
  
  // 获取下一个可用的token配置
  getNextToken(): TokenConfig | null {
    const availableConfigs = this.configs.filter(c => c.active && c.errorCount < 5);
    
    if (availableConfigs.length === 0) {
      console.warn("No available tokens, resetting error counts");
      // 重置错误计数
      this.configs.forEach(c => c.errorCount = 0);
      return this.configs[0] || null;
    }
    
    // 轮询策略：选择最少使用的token
    availableConfigs.sort((a, b) => a.lastUsed - b.lastUsed);
    const selected = availableConfigs[0];
    selected.lastUsed = Date.now();
    
    return selected;
  }
  
  // 标记token出错
  markError(token: string) {
    const config = this.configs.find(c => c.token === token);
    if (config) {
      config.errorCount++;
      if (config.errorCount >= 5) {
        config.active = false;
        console.warn(`Token ${token.substring(0, 10)}... disabled due to errors`);
      }
    }
  }
  
  // 标记token成功
  markSuccess(token: string) {
    const config = this.configs.find(c => c.token === token);
    if (config) {
      config.errorCount = Math.max(0, config.errorCount - 1);
      config.active = true;
    }
  }
  
  // 获取状态信息
  getStatus() {
    return this.configs.map(c => ({
      token: c.token.substring(0, 10) + "...",
      endpoint: c.endpoint,
      active: c.active,
      errorCount: c.errorCount,
      lastUsed: new Date(c.lastUsed).toISOString()
    }));
  }
}

// 全局token管理器
const tokenManager = new TokenManager();

// 模型映射
const ALIAS_MODEL = "z-ai/glm-4.5";
const models = [
  {
    id: ALIAS_MODEL,
    object: "model",
    owned_by: "z-ai",
    permission: [],
  },
];

console.log("Starting enhanced OpenAI-compatible proxy on http://localhost:8000");
console.log("Environment variables:");
console.log("- API_TOKENS: comma-separated list of tokens");
console.log("- API_ENDPOINTS: comma-separated list of endpoints");
console.log("- API_MODELS: comma-separated list of models");

serve(async (req) => {
  const url = new URL(req.url);

  // Handle CORS preflight
  if (req.method === "OPTIONS") {
    return new Response(null, { status: 204, headers: corsHeaders });
  }

  // GET /v1/models
  if (req.method === "GET" && url.pathname === "/v1/models") {
    return new Response(JSON.stringify({ object: "list", data: models }), {
      status: 200,
      headers: { "Content-Type": "application/json", ...corsHeaders },
    });
  }

  // GET /status - 新增状态端点
  if (req.method === "GET" && url.pathname === "/status") {
    return new Response(JSON.stringify({
      status: "running",
      tokens: tokenManager.getStatus(),
      timestamp: new Date().toISOString()
    }), {
      status: 200,
      headers: { "Content-Type": "application/json", ...corsHeaders },
    });
  }

  // POST /v1/chat/completions
  if (req.method === "POST" && url.pathname === "/v1/chat/completions") {
    const tokenConfig = tokenManager.getNextToken();
    
    if (!tokenConfig) {
      return new Response(JSON.stringify({ 
        error: "No available tokens" 
      }), {
        status: 503,
        headers: { "Content-Type": "application/json", ...corsHeaders },
      });
    }

    try {
      // Parse incoming request
      const { model, stream = false, ...rest } = await req.json();
      const isStream = Boolean(stream);
      const targetModel = model === ALIAS_MODEL ? tokenConfig.model : model;

      console.log(`Using token ${tokenConfig.token.substring(0, 10)}... for model ${targetModel}`);

      // 构建请求头，使用选中的token
      const upstreamHeaders = new Headers();
      upstreamHeaders.set("Content-Type", "application/json");
      upstreamHeaders.set("Authorization", `Bearer ${tokenConfig.token}`);
      
      // 复制其他必要的头部（除了Authorization）
      for (const [key, value] of req.headers.entries()) {
        if (key.toLowerCase() !== "authorization" && key.toLowerCase() !== "content-type") {
          upstreamHeaders.set(key, value);
        }
      }

      // Always fetch upstream with streaming enabled
      const upstreamResponse = await fetch(tokenConfig.endpoint, {
        method: "POST",
        headers: upstreamHeaders,
        body: JSON.stringify({ model: targetModel, stream: true, ...rest }),
      });

      if (!upstreamResponse.ok) {
        tokenManager.markError(tokenConfig.token);
        return new Response(JSON.stringify({ 
          error: `Upstream error: ${upstreamResponse.status}` 
        }), {
          status: upstreamResponse.status,
          headers: { "Content-Type": "application/json", ...corsHeaders },
        });
      }

      if (!upstreamResponse.body) {
        tokenManager.markError(tokenConfig.token);
        return new Response("Upstream response has no body", { 
          status: 500, 
          headers: corsHeaders 
        });
      }

      const reader = upstreamResponse.body.getReader();
      const decoder = new TextDecoder();
      let buffer = "";
      const chunks: any[] = [];

      // Read and transform all chunks
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;
        buffer += decoder.decode(value, { stream: true });
        let lines = buffer.split(/\r?\n/);
        buffer = lines.pop()!; // last incomplete line
        for (const line of lines) {
          if (!line.startsWith("data: ")) continue;
          const payload = line.slice(6).trim();
          if (payload === "[DONE]") break;
          try {
            const parsed = JSON.parse(payload);
            if (parsed.data) {
              delete parsed.data.edit_index;
              delete parsed.data.edit_content;
              if (typeof parsed.data.delta_content === 'string') {
                parsed.data.delta_content = parsed.data.delta_content
                  .replace(/<details[^>]*>/g, '<think>')
                  .replace(/<\/details>/g, '</think>')
                  .replace(/<summary>.*?<\/summary>/g, '')
                  .trimStart();
              }
            }
            chunks.push(parsed);
          } catch {
            // skip non-JSON lines
          }
        }
      }

      // 标记成功
      tokenManager.markSuccess(tokenConfig.token);

      if (isStream) {
        // Stream back transformed chunks
        const streamController = new TransformStream({
          start(controller) {
            for (const parsed of chunks) {
              controller.enqueue(new TextEncoder().encode(`data: ${JSON.stringify(parsed)}\n\n`));
            }
            controller.enqueue(new TextEncoder().encode("data: [DONE]\n\n"));
            controller.close();
          }
        });
        return new Response(streamController.readable, {
          status: 200,
          headers: {
            ...corsHeaders,
            "Content-Type": "text/event-stream",
            "Cache-Control": "no-cache",
            Connection: "keep-alive",
          },
        });
      } else {
        // Non-stream: aggregate into single OpenAI-style response
        const full = {
          id: chunks[0]?.data?.id || null,
          object: 'chat.completion',
          created: Math.floor(Date.now() / 1000),
          model: model,
          choices: [
            {
              index: 0,
              message: { role: 'assistant', content: chunks.map(c => c.data?.delta_content || '').join('') },
              finish_reason: 'stop',
            }
          ]
        };
        return new Response(JSON.stringify(full), {
          status: 200,
          headers: { "Content-Type": "application/json", ...corsHeaders },
        });
      }

    } catch (error) {
      console.error("Request failed:", error);
      tokenManager.markError(tokenConfig.token);
      return new Response(JSON.stringify({ 
        error: "Internal server error" 
      }), {
        status: 500,
        headers: { "Content-Type": "application/json", ...corsHeaders },
      });
    }
  }

  // Fallback
  return new Response(JSON.stringify({ error: "Not found" }), {
    status: 404,
    headers: { "Content-Type": "application/json", ...corsHeaders },
  });
});
