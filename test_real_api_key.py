#!/usr/bin/env python3
"""
测试真正的 API Key 创建功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from SambaNova_Auto import SambaNovaAuto

def test_real_api_key_creation():
    """测试真正的 API Key 创建功能"""
    print("🧪 测试真正的 API Key 创建功能...")
    
    sambanova = SambaNovaAuto()
    
    # 使用已有的账户信息进行测试
    sambanova.email_address = "<EMAIL>"  # 使用之前注册的账户
    sambanova.password = "jqV@D98Kl9#U"
    
    print("🔑 测试浏览器自动化创建 API Key...")
    
    try:
        api_key = sambanova.create_api_key_with_browser("Test API Key")
        
        if api_key:
            print(f"✅ API Key创建成功: {api_key}")
            
            # 测试保存功能
            sambanova.save_api_key_simple(api_key)
            print("✅ API Key保存测试完成")
            
            return True
        else:
            print("❌ API Key创建失败")
            return False
        
    except Exception as e:
        print(f"❌ API Key创建测试异常: {e}")
        import traceback
        print(f"🔍 详细错误: {traceback.format_exc()}")
        return False

def main():
    """主测试函数"""
    print("🔧 真正的 API Key 创建功能测试")
    print("=" * 50)
    
    success = test_real_api_key_creation()
    
    if success:
        print("\n🎉 测试成功！真正的 API Key 创建功能可用")
        print("💡 现在可以在 https://cloud.sambanova.ai/apis 界面创建 API Key")
    else:
        print("\n❌ 测试失败！请检查浏览器自动化逻辑")

if __name__ == "__main__":
    main()
