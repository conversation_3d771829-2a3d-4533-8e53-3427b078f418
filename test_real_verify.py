#!/usr/bin/env python3
"""
测试真实验证链接
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from SambaNova_Auto import SambaNovaAuto

def test_real_verification_link():
    """测试真实的验证链接"""
    print("🧪 测试真实验证链接...")
    
    sambanova = SambaNovaAuto()
    
    # 使用您最新提供的真实验证链接
    real_link = "https://auth0.sambanova.ai/u/email-verification?ticket=BZBFdm0DgiE8u2wfZPf4FRRYcvYgARRb#"
    
    print(f"🔗 真实验证链接: {real_link}")
    
    try:
        # 先用HTTP客户端直接访问看看内容
        headers = {
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        }

        print("🔍 直接访问验证链接...")
        response = sambanova.client.get(
            real_link,
            headers=headers,
            follow_redirects=True,
            timeout=10
        )
        
        print(f"🔍 响应状态码: {response.status_code}")
        print(f"🔍 最终URL: {response.url}")
        print(f"🔍 页面内容前500字符:")
        print(response.text[:500])
        print("=" * 50)
        
        # 现在用快速验证方法
        print("\n🚀 使用快速验证方法...")
        result = sambanova.verify_email_fast(real_link)
        
        if result:
            print("✅ 验证成功")
        else:
            print("❌ 验证失败")
        
        return result
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        print(f"🔍 详细错误: {traceback.format_exc()}")
        return False

def main():
    """主测试函数"""
    print("🔧 真实验证链接测试")
    print("=" * 50)
    
    success = test_real_verification_link()
    
    if success:
        print("\n🎉 真实验证链接测试成功！")
    else:
        print("\n❌ 真实验证链接测试失败，需要调整验证逻辑")

if __name__ == "__main__":
    main()
