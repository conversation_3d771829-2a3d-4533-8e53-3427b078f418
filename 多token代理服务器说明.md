# 多Token轮询代理服务器

## 🎯 功能特点

### ✅ 新增功能
1. **多Token轮询** - 支持配置多个API Token进行负载均衡
2. **环境变量配置** - 通过环境变量灵活配置tokens和endpoints
3. **故障转移** - 自动检测失效token并切换到可用token
4. **错误恢复** - 自动重置错误计数，恢复失效token
5. **状态监控** - 提供`/status`端点查看token状态
6. **负载均衡** - 基于最少使用时间的轮询策略

### 🔧 环境变量配置

```bash
# 多个token，用逗号分隔
export API_TOKENS="token1,token2,token3"

# 对应的endpoints（可选，默认使用同一个）
export API_ENDPOINTS="https://api1.example.com/chat/completions,https://api2.example.com/chat/completions,https://api3.example.com/chat/completions"

# 对应的模型名称（可选，默认使用同一个）
export API_MODELS="model1,model2,model3"
```

## 🚀 使用方法

### 1. 基本启动
```bash
# 使用默认配置
deno run --allow-net --allow-env enhanced_proxy_server.ts
```

### 2. 多Token配置启动
```bash
# 设置环境变量
export API_TOKENS="sk-token1,sk-token2,sk-token3"
export API_ENDPOINTS="https://api1.com/v1/chat/completions,https://api2.com/v1/chat/completions,https://api3.com/v1/chat/completions"

# 启动服务器
deno run --allow-net --allow-env enhanced_proxy_server.ts
```

### 3. Windows PowerShell配置
```powershell
$env:API_TOKENS = "sk-token1,sk-token2,sk-token3"
$env:API_ENDPOINTS = "https://api1.com/v1/chat/completions,https://api2.com/v1/chat/completions,https://api3.com/v1/chat/completions"
deno run --allow-net --allow-env enhanced_proxy_server.ts
```

## 📊 API端点

### 1. 聊天完成
```http
POST /v1/chat/completions
Content-Type: application/json

{
  "model": "z-ai/glm-4.5",
  "messages": [
    {"role": "user", "content": "Hello"}
  ],
  "stream": false
}
```

### 2. 模型列表
```http
GET /v1/models
```

### 3. 状态监控（新增）
```http
GET /status
```

**响应示例**:
```json
{
  "status": "running",
  "tokens": [
    {
      "token": "sk-token1...",
      "endpoint": "https://api1.com/v1/chat/completions",
      "active": true,
      "errorCount": 0,
      "lastUsed": "2025-08-01T04:00:00.000Z"
    },
    {
      "token": "sk-token2...",
      "endpoint": "https://api2.com/v1/chat/completions", 
      "active": false,
      "errorCount": 5,
      "lastUsed": "2025-08-01T03:55:00.000Z"
    }
  ],
  "timestamp": "2025-08-01T04:05:00.000Z"
}
```

## 🔄 Token轮询策略

### 1. 负载均衡
- 选择最少使用的token（基于lastUsed时间）
- 权重支持（预留功能）

### 2. 故障处理
- 连续5次错误后自动禁用token
- 成功请求会减少错误计数
- 所有token失效时自动重置错误计数

### 3. 恢复机制
- 错误token会逐渐恢复（每次成功-1错误计数）
- 支持手动重置（重启服务器）

## 🛠️ 配置示例

### ChatFreely.xyz多账号配置
```bash
export API_TOKENS="sk-key1,sk-key2,sk-key3"
export API_ENDPOINTS="https://www.chatfreely.xyz/v1/chat/completions,https://www.chatfreely.xyz/v1/chat/completions,https://www.chatfreely.xyz/v1/chat/completions"
export API_MODELS="kimi-k2-instruct,deepseek-v3-0324,deepseek-r1-0528"
```

### 混合API配置
```bash
export API_TOKENS="chatfreely-key,openai-key,anthropic-key"
export API_ENDPOINTS="https://www.chatfreely.xyz/v1/chat/completions,https://api.openai.com/v1/chat/completions,https://api.anthropic.com/v1/messages"
export API_MODELS="kimi-k2-instruct,gpt-4,claude-3-sonnet"
```

## 📈 监控和调试

### 1. 查看token状态
```bash
curl http://localhost:8000/status
```

### 2. 日志输出
服务器会输出以下日志：
- Token配置加载信息
- 每次请求使用的token
- Token错误和恢复信息

### 3. 错误处理
- HTTP 503: 没有可用token
- HTTP 500: 上游服务器错误
- 自动重试机制

## 🔧 高级配置

### 1. Docker部署
```dockerfile
FROM denoland/deno:1.37.0

WORKDIR /app
COPY enhanced_proxy_server.ts .

ENV API_TOKENS=""
ENV API_ENDPOINTS=""
ENV API_MODELS=""

EXPOSE 8000

CMD ["run", "--allow-net", "--allow-env", "enhanced_proxy_server.ts"]
```

### 2. 系统服务配置
```ini
[Unit]
Description=Multi-Token Proxy Server
After=network.target

[Service]
Type=simple
User=proxy
WorkingDirectory=/opt/proxy
Environment=API_TOKENS=token1,token2,token3
Environment=API_ENDPOINTS=endpoint1,endpoint2,endpoint3
ExecStart=/usr/local/bin/deno run --allow-net --allow-env enhanced_proxy_server.ts
Restart=always

[Install]
WantedBy=multi-user.target
```

## 🎯 与原版对比

| 功能 | 原版 | 增强版 |
|------|------|--------|
| Token数量 | 单个 | 多个 |
| 负载均衡 | ❌ | ✅ |
| 故障转移 | ❌ | ✅ |
| 环境变量配置 | ❌ | ✅ |
| 状态监控 | ❌ | ✅ |
| 错误恢复 | ❌ | ✅ |
| 配置灵活性 | 低 | 高 |

## 🏆 总结

增强版代理服务器完全支持：
- ✅ **多Token轮询** - 通过环境变量配置多个token
- ✅ **外部环境设置** - 完全支持环境变量配置
- ✅ **负载均衡** - 智能选择最优token
- ✅ **故障转移** - 自动处理失效token
- ✅ **监控功能** - 实时查看token状态

这个增强版本解决了原版代码不支持多token的问题，提供了生产级的可靠性和可配置性。
