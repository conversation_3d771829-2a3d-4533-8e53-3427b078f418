#!/usr/bin/env python3
"""
SambaNova 修复验证脚本
测试修复后的关键功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from SambaNova_Auto import SambaNovaAuto
import urllib.parse

def test_error_detection():
    """测试错误检测逻辑"""
    print("🧪 测试错误检测逻辑...")
    
    # 模拟包含错误的重定向URL
    test_url = "https://cloud.sambanova.ai/web/auth/callback?error=access_denied&error_description=Please%20verify%20your%20email%20before%20continuing.&state=test123"
    
    # 解析URL
    parsed = urllib.parse.urlparse(test_url)
    query_params = urllib.parse.parse_qs(parsed.query)
    
    error = query_params.get("error", [None])[0]
    error_description = query_params.get("error_description", [None])[0]
    
    print(f"🔍 提取的错误: {error}")
    print(f"🔍 错误描述: {error_description}")
    
    # 测试检测逻辑
    if error == 'access_denied' and error_description and 'verify' in error_description.lower():
        print("✅ 错误检测逻辑正常工作")
        return True
    else:
        print("❌ 错误检测逻辑有问题")
        return False

def test_password_strength():
    """测试密码强度"""
    print("\n🧪 测试密码强度...")
    
    sambanova = SambaNovaAuto()
    
    # 生成5个密码并测试
    all_valid = True
    for i in range(5):
        password = sambanova.generate_password()
        
        # 检查密码强度
        has_lower = any(c.islower() for c in password)
        has_upper = any(c.isupper() for c in password)
        has_digit = any(c.isdigit() for c in password)
        has_special = any(c in "!@#$%^&*" for c in password)
        
        types_count = sum([has_lower, has_upper, has_digit, has_special])
        is_valid = len(password) >= 8 and types_count >= 3
        
        print(f"密码 {i+1}: {password} - {'✅' if is_valid else '❌'}")
        
        if not is_valid:
            all_valid = False
    
    if all_valid:
        print("✅ 所有密码都符合要求")
        return True
    else:
        print("❌ 部分密码不符合要求")
        return False

def test_verification_logic():
    """测试验证逻辑改进"""
    print("\n🧪 测试验证逻辑改进...")

    # 测试严格的成功标志检测
    test_cases = [
        {
            "url": "https://auth0.sambanova.ai/continue?success=true",
            "content": "Your email was verified successfully",
            "should_pass": True
        },
        {
            "url": "https://auth0.sambanova.ai/result?code=success",
            "content": "Email verification successful",
            "should_pass": True
        },
        {
            "url": "https://auth0.sambanova.ai/verify?email%20was%20verified",
            "content": "Continue using the application",
            "should_pass": True
        },
        {
            "url": "https://auth0.sambanova.ai/error?code=expired",
            "content": "Verification link has expired",
            "should_pass": False
        }
    ]

    # 使用新的严格检测逻辑
    success_url_indicators = [
        'success=true',
        'verified=true',
        'code=success',
        'email%20was%20verified',
        'verification%20successful'
    ]

    success_content_indicators = [
        'email was verified',
        'verification successful',
        'account verified',
        'email verified successfully',
        'continue using the application'
    ]

    all_correct = True
    for case in test_cases:
        url = case["url"].lower()
        content = case["content"].lower()
        expected = case["should_pass"]

        url_success = any(indicator in url for indicator in success_url_indicators)
        content_success = any(indicator in content for indicator in success_content_indicators)
        actual = url_success or content_success

        result = "✅" if actual == expected else "❌"
        print(f"测试: {case['url'][:50]}... - {result}")

        if actual != expected:
            all_correct = False

    if all_correct:
        print("✅ 验证成功标志检测正常")
        return True
    else:
        print("❌ 验证成功标志检测有问题")
        return False

def main():
    """主测试函数"""
    print("🔧 SambaNova 修复验证测试")
    print("=" * 50)
    
    tests = [
        ("错误检测逻辑", test_error_detection),
        ("密码强度", test_password_strength),
        ("验证逻辑", test_verification_logic),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 50)
    print("📊 测试结果汇总:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 总体结果: {passed}/{len(results)} 项测试通过")
    
    if passed == len(results):
        print("🎉 所有测试通过，修复成功！")
        return True
    else:
        print("⚠️ 部分测试失败，需要进一步修复")
        return False

if __name__ == "__main__":
    main()
