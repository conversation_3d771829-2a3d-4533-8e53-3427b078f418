#!/usr/bin/env python3
"""
测试邮件接收功能
"""

import httpx
import random
import string
import time
from bs4 import BeautifulSoup

def generate_random_email_name():
    """生成随机邮箱名称（7位小写字母）"""
    return ''.join(random.choice(string.ascii_lowercase) for _ in range(7))

def test_email_receive():
    """测试邮件接收功能"""
    
    # 支持的域名列表
    email_domains = [
        '@mailto.plus',
        '@fexpost.com', 
        '@fexbox.org',
        '@mailbox.in.ua',
        '@rover.info',
        '@chitthi.in',
        '@fextemp.com',
        '@any.pink',
        '@merepost.com'
    ]
    
    # 生成测试邮箱
    email_name = generate_random_email_name()
    domain = random.choice(email_domains)
    test_email = email_name + domain
    
    print(f"📧 测试邮箱: {test_email}")
    
    # 创建HTTP客户端
    client = httpx.Client(timeout=30.0)
    
    try:
        print("📬 开始监控邮件...")
        print("💡 提示: 您可以向这个邮箱发送测试邮件来验证接收功能")
        print(f"📮 邮箱地址: {test_email}")
        print("⏰ 将监控 60 秒...")
        
        for attempt in range(20):  # 监控 60 秒 (20次 * 3秒)
            print(f"📧 检查邮件 (第 {attempt + 1}/20 次)...")
            
            response = client.get(
                'https://tempmail.plus/api/mails',
                params={
                    'email': test_email,
                    'limit': 20,
                    'epin': ''
                },
                headers={
                    'referer': 'https://tempmail.plus/',
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                }
            )
            
            if response.status_code == 200:
                data = response.json()
                messages = data.get('mail_list', [])
                
                if messages:
                    print(f"🎉 收到 {len(messages)} 封邮件!")
                    
                    for i, msg in enumerate(messages, 1):
                        print(f"\n📨 邮件 {i}:")
                        print(f"   发件人: {msg.get('from', 'N/A')}")
                        print(f"   主题: {msg.get('subject', 'N/A')}")
                        print(f"   时间: {msg.get('date', 'N/A')}")
                        
                        # 检查内容
                        content = msg.get('content', '') or msg.get('body', '') or msg.get('text', '')
                        if content:
                            # 尝试解析HTML内容
                            soup = BeautifulSoup(content, 'html.parser')
                            text_content = soup.get_text()[:200]  # 前200字符
                            print(f"   内容预览: {text_content}...")
                            
                            # 查找链接
                            links = soup.find_all('a', href=True)
                            if links:
                                print(f"   包含 {len(links)} 个链接:")
                                for link in links[:3]:  # 显示前3个链接
                                    print(f"     - {link['href']}")
                    
                    return True
                else:
                    print(f"   📭 暂无邮件 (count: {data.get('count', 0)})")
            else:
                print(f"❌ API调用失败: {response.status_code}")
            
            if attempt < 19:
                print("⏰ 等待 3 秒后重试...")
                time.sleep(3)
        
        print("\n⏰ 监控时间结束，未收到邮件")
        return False
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False
    finally:
        client.close()

if __name__ == "__main__":
    print("🚀 开始测试邮件接收功能...")
    print("📝 这个测试将生成一个临时邮箱并监控邮件接收")
    
    success = test_email_receive()
    
    if success:
        print("\n✅ 邮件接收测试成功!")
    else:
        print("\n📭 未收到邮件，但API功能正常")
