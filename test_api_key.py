#!/usr/bin/env python3
"""
测试API Key创建功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from SambaNova_Auto import SambaNovaAuto

def test_api_key_creation():
    """测试API Key创建功能"""
    print("🧪 测试API Key创建功能...")
    
    sambanova = SambaNovaAuto()
    
    # 模拟一个Access Token
    sambanova.access_token = "test_token_123"
    sambanova.email_address = "<EMAIL>"
    sambanova.password = "test_password"
    
    print("🔑 测试API Key创建...")
    
    try:
        api_key = sambanova.create_api_key("Test API Key")
        
        if api_key:
            print(f"✅ API Key创建成功: {api_key}")
            
            # 测试保存功能
            sambanova.save_api_key_simple(api_key)
            print("✅ API Key保存测试完成")
        else:
            print("⚠️ API Key创建失败（这是预期的，因为使用的是测试token）")
        
        return True
        
    except Exception as e:
        print(f"❌ API Key创建测试异常: {e}")
        return False

def main():
    """主测试函数"""
    print("🔧 API Key创建功能测试")
    print("=" * 50)
    
    success = test_api_key_creation()
    
    if success:
        print("\n🎉 测试完成！API Key创建功能可用")
    else:
        print("\n❌ 测试失败！请检查API Key创建逻辑")

if __name__ == "__main__":
    main()
