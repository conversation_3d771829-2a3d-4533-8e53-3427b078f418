#!/usr/bin/env python3
"""
代理池管理系统
"""

import httpx
import random
import time
import asyncio
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading
from typing import List, Dict, Optional
from subscription_proxy_parser import SubscriptionProxyParser

class ProxyPool:
    def __init__(self):
        # 只使用高质量、小规模的代理源
        self.proxy_sources = [
            # 🌟 顶级代理源 - 每5分钟更新
            "https://raw.githubusercontent.com/proxifly/free-proxy-list/main/proxies/all/data.txt",

            # 🔥 精选高质量GitHub代理源（小规模）
            "https://raw.githubusercontent.com/monosans/proxy-list/main/proxies/http.txt",
            "https://raw.githubusercontent.com/mmpx12/proxy-list/master/http.txt",
            "https://raw.githubusercontent.com/ShiftyTR/Proxy-List/master/http.txt",
            "https://raw.githubusercontent.com/prxchk/proxy-list/main/http.txt"
        ]
        
        self.working_proxies = []
        self.failed_proxies = set()
        self.lock = threading.Lock()

        # 订阅链接列表
        self.subscription_urls = [
            "https://tts.wo.sd/sub/normal/271438ac-8822-430e-adda-34695be64dea?app=singbox"
        ]
        
    def fetch_proxies_from_source(self, source_url: str) -> List[str]:
        """从单个源获取代理列表"""
        try:
            print(f"🔍 获取代理源: {source_url}")
            
            with httpx.Client(timeout=15) as client:
                response = client.get(source_url)
                
            if response.status_code == 200:
                proxies = []
                lines = response.text.strip().split('\n')
                
                for line in lines:
                    line = line.strip()
                    if line and ':' in line:
                        # 验证IP:PORT格式
                        parts = line.split(':')
                        if len(parts) == 2:
                            try:
                                ip = parts[0].strip()
                                port = int(parts[1].strip())
                                if 1 <= port <= 65535:
                                    proxies.append(f"{ip}:{port}")
                            except ValueError:
                                continue
                
                print(f"✅ 从 {source_url} 获取到 {len(proxies)} 个代理")
                return proxies
            else:
                print(f"❌ 获取失败: {source_url} - {response.status_code}")
                return []
                
        except Exception as e:
            print(f"❌ 获取异常: {source_url} - {e}")
            return []
    
    def fetch_subscription_proxies(self) -> List[str]:
        """从订阅链接获取代理"""
        print("🔗 开始获取订阅链接代理...")
        subscription_proxies = []

        parser = SubscriptionProxyParser()
        for url in self.subscription_urls:
            try:
                proxies = parser.parse_subscription(url)
                subscription_proxies.extend(proxies)
                print(f"✅ 从订阅获取到 {len(proxies)} 个代理")
            except Exception as e:
                print(f"❌ 订阅解析失败: {url} - {e}")

        return subscription_proxies

    def fetch_all_proxies(self) -> List[str]:
        """从所有源获取代理列表"""
        print("🌐 开始获取代理列表...")
        all_proxies = []

        # 1. 优先获取订阅链接代理（高质量）
        subscription_proxies = self.fetch_subscription_proxies()
        all_proxies.extend(subscription_proxies)

        # 如果订阅代理足够多，就不获取免费代理了
        if len(subscription_proxies) >= 20:
            print(f"✅ 订阅代理充足({len(subscription_proxies)}个)，跳过免费代理源")
            return subscription_proxies

        # 2. 补充获取免费代理源（限制数量）
        print("🔄 补充获取免费代理源...")
        with ThreadPoolExecutor(max_workers=3) as executor:
            future_to_source = {
                executor.submit(self.fetch_proxies_from_source, source): source
                for source in self.proxy_sources[:3]  # 只使用前3个源
            }

            for future in as_completed(future_to_source):
                source = future_to_source[future]
                try:
                    proxies = future.result()
                    all_proxies.extend(proxies)
                except Exception as e:
                    print(f"❌ 线程异常: {source} - {e}")

        # 去重并限制总数
        unique_proxies = list(set(all_proxies))
        if len(unique_proxies) > 500:
            unique_proxies = unique_proxies[:500]  # 限制最多500个

        print(f"📊 总共获取到 {len(unique_proxies)} 个唯一代理")
        return unique_proxies
    
    def test_proxy(self, proxy: str, test_url: str = "http://httpbin.org/ip", timeout: int = 15) -> Dict:
        """测试单个代理的可用性"""
        try:
            proxy_url = f"http://{proxy}"

            start_time = time.time()

            with httpx.Client(proxy=proxy_url, timeout=timeout) as client:
                response = client.get(test_url)
                
            end_time = time.time()
            response_time = round((end_time - start_time) * 1000, 2)  # 毫秒
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    proxy_ip = data.get('origin', 'unknown')
                    return {
                        'proxy': proxy,
                        'status': 'working',
                        'response_time': response_time,
                        'proxy_ip': proxy_ip
                    }
                except:
                    return {
                        'proxy': proxy,
                        'status': 'working',
                        'response_time': response_time,
                        'proxy_ip': 'unknown'
                    }
            else:
                return {
                    'proxy': proxy,
                    'status': 'failed',
                    'error': f'HTTP {response.status_code}'
                }
                
        except Exception as e:
            return {
                'proxy': proxy,
                'status': 'failed',
                'error': str(e)
            }
    
    def test_proxies_batch(self, proxies: List[str], max_workers: int = 50) -> List[Dict]:
        """批量测试代理"""
        print(f"🧪 开始测试 {len(proxies)} 个代理...")
        
        working_proxies = []
        tested_count = 0
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            future_to_proxy = {
                executor.submit(self.test_proxy, proxy): proxy 
                for proxy in proxies
            }
            
            for future in as_completed(future_to_proxy):
                proxy = future_to_proxy[future]
                tested_count += 1
                
                try:
                    result = future.result()
                    
                    if result['status'] == 'working':
                        working_proxies.append(result)
                        print(f"✅ {proxy} - {result['response_time']}ms - IP: {result['proxy_ip']}")
                    else:
                        print(f"❌ {proxy} - {result['error']}")
                        
                except Exception as e:
                    print(f"❌ {proxy} - 测试异常: {e}")
                
                # 显示进度
                if tested_count % 10 == 0:
                    print(f"📊 已测试: {tested_count}/{len(proxies)}, 可用: {len(working_proxies)}")
        
        # 按响应时间排序
        working_proxies.sort(key=lambda x: x['response_time'])
        
        print(f"🎉 测试完成！可用代理: {len(working_proxies)}/{len(proxies)}")
        return working_proxies
    
    def get_working_proxy(self) -> Optional[str]:
        """获取一个可用的代理"""
        with self.lock:
            if self.working_proxies:
                proxy_info = random.choice(self.working_proxies)
                return proxy_info['proxy']
            return None
    
    def mark_proxy_failed(self, proxy: str):
        """标记代理为失败"""
        with self.lock:
            self.failed_proxies.add(proxy)
            # 从可用列表中移除
            self.working_proxies = [p for p in self.working_proxies if p['proxy'] != proxy]
    
    def refresh_proxy_pool(self, max_test_proxies: int = 100):
        """刷新代理池"""
        print("🔄 刷新代理池...")

        # 获取所有代理
        all_proxies = self.fetch_all_proxies()

        if not all_proxies:
            print("❌ 未获取到任何代理")
            return False

        # 随机选择一部分进行测试（避免测试时间过长）
        if len(all_proxies) > max_test_proxies:
            test_proxies = random.sample(all_proxies, max_test_proxies)
            print(f"🎲 随机选择 {max_test_proxies} 个代理进行测试")
        else:
            test_proxies = all_proxies

        # 测试代理
        working_proxies = self.test_proxies_batch(test_proxies, max_workers=30)

        # 只保留响应时间较快的代理（小于30秒）
        fast_proxies = [p for p in working_proxies if p['response_time'] < 30000]

        with self.lock:
            self.working_proxies = fast_proxies
            self.failed_proxies.clear()

        print(f"⚡ 筛选出 {len(fast_proxies)} 个快速代理（<30秒）")
        return len(fast_proxies) > 0


def main():
    """测试代理池功能"""
    print("🌐 代理池测试程序")
    print("=" * 50)
    
    proxy_pool = ProxyPool()
    
    # 刷新代理池
    success = proxy_pool.refresh_proxy_pool(max_test_proxies=100)
    
    if success:
        print(f"\n🎉 代理池初始化成功！")
        print(f"📊 可用代理数量: {len(proxy_pool.working_proxies)}")
        
        # 显示前10个最快的代理
        print("\n⚡ 最快的10个代理:")
        for i, proxy_info in enumerate(proxy_pool.working_proxies[:10], 1):
            print(f"  {i}. {proxy_info['proxy']} - {proxy_info['response_time']}ms")
        
        # 测试获取随机代理
        print("\n🎲 随机代理测试:")
        for i in range(5):
            proxy = proxy_pool.get_working_proxy()
            print(f"  随机代理 {i+1}: {proxy}")
        
        return True
    else:
        print("❌ 代理池初始化失败")
        return False


if __name__ == "__main__":
    main()
