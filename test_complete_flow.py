#!/usr/bin/env python3
"""
测试完整的注册流程
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from SambaNova_Auto import SambaNovaAuto

def test_complete_registration():
    """测试完整的注册流程"""
    print("🧪 测试完整的注册流程...")
    print("=" * 60)
    
    sambanova = SambaNovaAuto()
    
    try:
        # 开始注册流程
        print("🚀 开始注册流程...")
        result = sambanova.register_account()
        
        if result:
            print("\n🎉 注册流程完成！")
            
            # 检查生成的文件
            if os.path.exists("sambanova_keys.txt"):
                print("📁 检查生成的文件...")
                with open("sambanova_keys.txt", 'r', encoding='utf-8') as f:
                    content = f.read()
                    print(f"📄 文件内容:\n{content}")
            else:
                print("❌ 未找到 sambanova_keys.txt 文件")
            
            return True
        else:
            print("\n❌ 注册流程失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        print(f"🔍 详细错误: {traceback.format_exc()}")
        return False

def test_verification_only():
    """仅测试验证功能"""
    print("\n🔧 测试验证功能...")
    print("=" * 60)
    
    sambanova = SambaNovaAuto()
    
    # 使用一个示例验证链接
    test_links = [
        "https://auth0.sambanova.ai/u/email-verification?ticket=BZBFdm0DgiE8u2wfZPf4FRRYcvYgARRb#",
        "https://auth0.sambanova.ai/u/email-verification?ticket=test123"
    ]
    
    for i, link in enumerate(test_links, 1):
        print(f"\n🔗 测试链接 {i}: {link}")
        result = sambanova.verify_email_fast(link)
        print(f"结果: {'✅ 成功' if result else '❌ 失败'}")

def main():
    """主测试函数"""
    print("🔧 SambaNova 完整流程测试")
    print("=" * 60)
    
    # 选择测试模式
    print("请选择测试模式:")
    print("1. 完整注册流程测试")
    print("2. 仅验证功能测试")
    print("3. 两者都测试")
    
    try:
        choice = input("请输入选择 (1/2/3): ").strip()
        
        if choice == "1":
            success = test_complete_registration()
        elif choice == "2":
            test_verification_only()
            success = True
        elif choice == "3":
            success1 = test_complete_registration()
            test_verification_only()
            success = success1
        else:
            print("❌ 无效选择")
            return
        
        if success:
            print("\n🎉 测试完成！")
        else:
            print("\n❌ 测试失败！")
            
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断测试")
    except Exception as e:
        print(f"\n❌ 测试异常: {e}")

if __name__ == "__main__":
    main()
