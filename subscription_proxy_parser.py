#!/usr/bin/env python3
"""
订阅链接代理解析器
支持解析VLESS、Trojan、VMess等协议的订阅链接
"""

import base64
import urllib.parse
import re
import httpx
from typing import List, Dict, Optional

class SubscriptionProxyParser:
    def __init__(self):
        self.client = httpx.Client(timeout=30)
        
    def fetch_subscription(self, url: str) -> str:
        """获取订阅链接内容"""
        try:
            print(f"🔍 获取订阅链接: {url}")
            response = self.client.get(url)
            
            if response.status_code == 200:
                content = response.text.strip()
                print(f"✅ 订阅内容长度: {len(content)} 字符")
                return content
            else:
                print(f"❌ 获取失败: {response.status_code}")
                return ""
        except Exception as e:
            print(f"❌ 获取异常: {e}")
            return ""
    
    def decode_base64_subscription(self, content: str) -> List[str]:
        """解码Base64订阅内容"""
        try:
            # 尝试Base64解码
            decoded_bytes = base64.b64decode(content)
            decoded_text = decoded_bytes.decode('utf-8')
            
            # 按行分割
            lines = decoded_text.strip().split('\n')
            proxy_lines = [line.strip() for line in lines if line.strip()]
            
            print(f"✅ 解码成功，找到 {len(proxy_lines)} 个代理配置")
            return proxy_lines
            
        except Exception as e:
            print(f"❌ Base64解码失败: {e}")
            return []
    
    def parse_vless_proxy(self, vless_url: str) -> Optional[Dict]:
        """解析VLESS代理配置"""
        try:
            # vless://uuid@host:port?params#name
            if not vless_url.startswith('vless://'):
                return None
            
            # 移除协议前缀
            url_part = vless_url[8:]
            
            # 分离名称部分
            if '#' in url_part:
                url_part, name = url_part.split('#', 1)
                name = urllib.parse.unquote(name)
            else:
                name = "Unknown"
            
            # 分离参数部分
            if '?' in url_part:
                auth_host, params = url_part.split('?', 1)
            else:
                auth_host = url_part
                params = ""
            
            # 解析认证和主机信息
            if '@' in auth_host:
                uuid, host_port = auth_host.split('@', 1)
            else:
                return None
            
            # 解析主机和端口
            if ':' in host_port:
                host, port = host_port.rsplit(':', 1)
                port = int(port)
            else:
                host = host_port
                port = 443
            
            # 清理IPv6地址的方括号
            if host.startswith('[') and host.endswith(']'):
                host = host[1:-1]
            
            return {
                'protocol': 'vless',
                'uuid': uuid,
                'host': host,
                'port': port,
                'name': name,
                'raw_url': vless_url
            }
            
        except Exception as e:
            print(f"❌ VLESS解析失败: {e}")
            return None
    
    def parse_trojan_proxy(self, trojan_url: str) -> Optional[Dict]:
        """解析Trojan代理配置"""
        try:
            # trojan://password@host:port?params#name
            if not trojan_url.startswith('trojan://'):
                return None
            
            # 移除协议前缀
            url_part = trojan_url[9:]
            
            # 分离名称部分
            if '#' in url_part:
                url_part, name = url_part.split('#', 1)
                name = urllib.parse.unquote(name)
            else:
                name = "Unknown"
            
            # 分离参数部分
            if '?' in url_part:
                auth_host, params = url_part.split('?', 1)
            else:
                auth_host = url_part
                params = ""
            
            # 解析认证和主机信息
            if '@' in auth_host:
                password, host_port = auth_host.split('@', 1)
            else:
                return None
            
            # 解析主机和端口
            if ':' in host_port:
                host, port = host_port.rsplit(':', 1)
                port = int(port)
            else:
                host = host_port
                port = 443
            
            # 清理IPv6地址的方括号
            if host.startswith('[') and host.endswith(']'):
                host = host[1:-1]
            
            return {
                'protocol': 'trojan',
                'password': password,
                'host': host,
                'port': port,
                'name': name,
                'raw_url': trojan_url
            }
            
        except Exception as e:
            print(f"❌ Trojan解析失败: {e}")
            return None
    
    def extract_http_proxies(self, proxy_configs: List[Dict]) -> List[str]:
        """从代理配置中提取可用于HTTP的代理"""
        http_proxies = []

        for config in proxy_configs:
            if not config:
                continue

            host = config.get('host', '')
            port = config.get('port', 0)

            # 跳过IPv6地址（暂不支持）
            if ':' in host and not host.startswith('['):
                continue

            # 支持更多端口，包括HTTPS代理
            if port in [80, 443, 8080, 3128, 8888, 1080, 9050]:
                proxy = f"{host}:{port}"
                http_proxies.append(proxy)
                print(f"✅ 提取代理: {proxy} ({config.get('name', 'Unknown')})")

        return http_proxies
    
    def parse_subscription(self, url: str) -> List[str]:
        """解析订阅链接并返回HTTP代理列表"""
        print("🌐 开始解析订阅链接...")
        
        # 获取订阅内容
        content = self.fetch_subscription(url)
        if not content:
            return []
        
        # 解码Base64内容
        proxy_lines = self.decode_base64_subscription(content)
        if not proxy_lines:
            return []
        
        # 解析代理配置
        proxy_configs = []
        for line in proxy_lines:
            if line.startswith('vless://'):
                config = self.parse_vless_proxy(line)
                if config:
                    proxy_configs.append(config)
            elif line.startswith('trojan://'):
                config = self.parse_trojan_proxy(line)
                if config:
                    proxy_configs.append(config)
        
        print(f"📊 成功解析 {len(proxy_configs)} 个代理配置")
        
        # 提取HTTP代理
        http_proxies = self.extract_http_proxies(proxy_configs)
        
        print(f"🎯 提取到 {len(http_proxies)} 个HTTP代理")
        return http_proxies


def test_subscription_parser():
    """测试订阅解析器"""
    print("🧪 测试订阅链接解析器")
    print("=" * 50)
    
    # 测试订阅链接
    subscription_url = "https://tts.wo.sd/sub/normal/271438ac-8822-430e-adda-34695be64dea?app=singbox"
    
    parser = SubscriptionProxyParser()
    http_proxies = parser.parse_subscription(subscription_url)
    
    if http_proxies:
        print(f"\n🎉 成功提取到 {len(http_proxies)} 个HTTP代理:")
        for i, proxy in enumerate(http_proxies, 1):
            print(f"  {i}. {proxy}")
        return http_proxies
    else:
        print("\n❌ 未能提取到可用的HTTP代理")
        return []


if __name__ == "__main__":
    test_subscription_parser()
