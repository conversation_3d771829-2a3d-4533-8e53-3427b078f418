#!/usr/bin/env python3
"""
测试代理池在 SambaNova 注册中的应用
"""

from SambaNova_Auto import SambaNovaAuto
import time

def test_proxy_registration():
    """测试使用代理进行注册"""
    print("🧪 测试代理注册功能")
    print("=" * 50)
    
    # 测试代理池初始化
    print("\n1️⃣ 测试代理池初始化...")
    sambanova = SambaNovaAuto(use_proxy=True)
    
    if not sambanova.use_proxy:
        print("❌ 代理池初始化失败，无法进行测试")
        return False
    
    print(f"✅ 代理池初始化成功，当前代理: {sambanova.current_proxy}")
    
    # 测试网络连接
    print("\n2️⃣ 测试代理网络连接...")
    if sambanova.test_network_connection():
        print("✅ 代理网络连接正常")
    else:
        print("❌ 代理网络连接失败")
        return False
    
    # 测试代理切换
    print("\n3️⃣ 测试代理切换功能...")
    old_proxy = sambanova.current_proxy
    if sambanova.switch_proxy():
        new_proxy = sambanova.current_proxy
        print(f"✅ 代理切换成功: {old_proxy} → {new_proxy}")
    else:
        print("⚠️ 代理切换失败（可能没有更多可用代理）")
    
    # 测试获取临时邮箱（通过代理）
    print("\n4️⃣ 测试通过代理获取临时邮箱...")
    email = sambanova.get_email_address()
    if email:
        print(f"✅ 成功获取邮箱: {email}")
    else:
        print("❌ 获取邮箱失败")
        return False
    
    # 测试获取 SambaNova 配置（通过代理）
    print("\n5️⃣ 测试通过代理获取 SambaNova 配置...")
    if sambanova.get_sambanova_config():
        print("✅ 成功获取 SambaNova 配置")
    else:
        print("❌ 获取 SambaNova 配置失败")
        return False
    
    print("\n🎉 代理功能测试完成！")
    return True

def test_multiple_proxy_registrations():
    """测试多次代理注册"""
    print("\n🧪 测试多次代理注册")
    print("=" * 50)
    
    success_count = 0
    total_count = 3
    
    for i in range(total_count):
        print(f"\n📋 第 {i+1}/{total_count} 次注册测试...")
        
        sambanova = SambaNovaAuto(use_proxy=True)
        
        if not sambanova.use_proxy:
            print("❌ 代理池初始化失败")
            continue
        
        # 模拟注册流程的关键步骤
        try:
            # 1. 获取邮箱
            email = sambanova.get_email_address()
            if not email:
                print("❌ 获取邮箱失败")
                continue
            
            # 2. 获取配置
            if not sambanova.get_sambanova_config():
                print("❌ 获取配置失败")
                continue
            
            # 3. 测试网络连接
            if not sambanova.test_network_connection():
                print("❌ 网络连接失败")
                continue
            
            print(f"✅ 第 {i+1} 次测试成功")
            success_count += 1
            
        except Exception as e:
            print(f"❌ 第 {i+1} 次测试异常: {e}")
        
        # 间隔
        if i < total_count - 1:
            print("⏰ 等待 3 秒...")
            time.sleep(3)
    
    print(f"\n📊 多次注册测试结果: {success_count}/{total_count}")
    return success_count > 0

def main():
    """主测试函数"""
    print("🌐 SambaNova 代理注册测试程序")
    print("=" * 60)
    
    # 基础代理功能测试
    if test_proxy_registration():
        print("\n✅ 基础代理功能测试通过")
    else:
        print("\n❌ 基础代理功能测试失败")
        return
    
    # 询问是否进行多次注册测试
    choice = input("\n🤔 是否进行多次注册测试？(y/n): ").strip().lower()
    if choice in ['y', 'yes', '是']:
        if test_multiple_proxy_registrations():
            print("\n✅ 多次注册测试通过")
        else:
            print("\n❌ 多次注册测试失败")
    
    print("\n🎉 所有测试完成！")

if __name__ == "__main__":
    main()
