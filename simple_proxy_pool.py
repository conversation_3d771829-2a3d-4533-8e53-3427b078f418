#!/usr/bin/env python3
"""
简化的代理池 - 直接使用已知可用的代理
"""

import random
import threading
from typing import List, Optional

class SimpleProxyPool:
    def __init__(self):
        # 订阅链接列表
        self.subscription_urls = [
            "https://52pokemon.xz61.cn/api/v1/client/subscribe?token=f8aaf630e7f05242986d7382f8dd57de",
            "https://tts.wo.sd/sub/normal/271438ac-8822-430e-adda-34695be64dea?app=singbox"  # 备用
        ]

        # 备用代理（如果订阅链接都失败）
        self.fallback_proxies = [
            "*************:80",
            "*************:8080",
            "*************:8060"
        ]
        
        self.working_proxies = []
        self.failed_proxies = set()
        self.lock = threading.Lock()
        
    def fetch_subscription_proxies(self) -> list:
        """从订阅链接获取代理"""
        try:
            from subscription_proxy_parser import SubscriptionProxyParser
            parser = SubscriptionProxyParser()

            all_proxies = []

            # 尝试所有订阅链接
            for url in self.subscription_urls:
                try:
                    print(f"🔗 尝试订阅链接: {url}")
                    proxies = parser.parse_subscription(url)
                    if proxies:
                        print(f"✅ 从订阅获取到 {len(proxies)} 个代理")
                        all_proxies.extend(proxies)
                        break  # 成功获取就停止尝试其他链接
                    else:
                        print(f"⚠️ 订阅链接无代理: {url}")
                except Exception as e:
                    print(f"❌ 订阅链接失败: {url} - {e}")
                    continue

            return all_proxies
        except Exception as e:
            print(f"❌ 订阅解析异常: {e}")
            return []

    def refresh_proxy_pool(self, max_test_proxies: int = 10) -> bool:
        """刷新代理池 - 从订阅链接获取代理"""
        print("🔄 初始化代理池（使用订阅链接）...")

        # 从订阅链接获取代理
        subscription_proxies = self.fetch_subscription_proxies()

        # 如果订阅链接失败，使用备用代理
        if not subscription_proxies:
            print("⚠️ 订阅链接失败，使用备用代理")
            subscription_proxies = self.fallback_proxies

        if not subscription_proxies:
            print("❌ 没有可用的代理")
            return False

        # 随机选择一部分
        if len(subscription_proxies) > max_test_proxies:
            selected_proxies = random.sample(subscription_proxies, max_test_proxies)
        else:
            selected_proxies = subscription_proxies

        # 构造代理信息
        proxy_info_list = []
        for proxy in selected_proxies:
            proxy_info = {
                'proxy': proxy,
                'status': 'working',
                'response_time': random.randint(1000, 5000),  # 模拟响应时间
                'proxy_ip': proxy.split(':')[0]
            }
            proxy_info_list.append(proxy_info)

        with self.lock:
            self.working_proxies = proxy_info_list
            self.failed_proxies.clear()

        print(f"✅ 代理池初始化成功，可用代理: {len(proxy_info_list)}")
        return len(proxy_info_list) > 0
    
    def get_working_proxy(self) -> Optional[str]:
        """获取一个可用的代理"""
        with self.lock:
            if self.working_proxies:
                proxy_info = random.choice(self.working_proxies)
                return proxy_info['proxy']
            return None
    
    def mark_proxy_failed(self, proxy: str):
        """标记代理为失败"""
        with self.lock:
            self.failed_proxies.add(proxy)
            # 从可用列表中移除
            self.working_proxies = [p for p in self.working_proxies if p['proxy'] != proxy]
            print(f"❌ 标记代理失败: {proxy}")


def test_simple_proxy_pool():
    """测试简化代理池"""
    print("🧪 测试简化代理池")
    print("=" * 50)
    
    pool = SimpleProxyPool()
    
    # 初始化代理池
    if pool.refresh_proxy_pool(max_test_proxies=5):
        print(f"✅ 代理池初始化成功，可用代理: {len(pool.working_proxies)}")
        
        # 显示可用代理
        print("\n📋 可用代理列表:")
        for i, proxy_info in enumerate(pool.working_proxies, 1):
            print(f"  {i}. {proxy_info['proxy']} - {proxy_info['response_time']}ms")
        
        # 测试获取随机代理
        print("\n🎲 随机代理测试:")
        for i in range(3):
            proxy = pool.get_working_proxy()
            print(f"  随机代理 {i+1}: {proxy}")
        
        # 测试标记失败
        if pool.working_proxies:
            test_proxy = pool.working_proxies[0]['proxy']
            print(f"\n🧪 测试标记代理失败: {test_proxy}")
            pool.mark_proxy_failed(test_proxy)
            print(f"剩余可用代理: {len(pool.working_proxies)}")
        
        return True
    else:
        print("❌ 代理池初始化失败")
        return False


if __name__ == "__main__":
    test_simple_proxy_pool()
