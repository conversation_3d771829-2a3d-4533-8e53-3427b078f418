#!/usr/bin/env python3
"""
机场订阅链接代理集成指南
展示如何在SambaNova注册中集成机场代理
"""

import httpx
import random
import time
from typing import List, Optional

class ProxyRotator:
    """代理轮换器 - 集成机场订阅链接"""
    
    def __init__(self, subscription_urls: List[str]):
        """
        初始化代理轮换器
        
        Args:
            subscription_urls: 机场订阅链接列表
        """
        self.subscription_urls = subscription_urls
        self.proxies = []
        self.current_index = 0
        self.failed_proxies = set()
        
    def load_proxies_from_subscription(self, url: str) -> List[str]:
        """从订阅链接加载代理"""
        try:
            print(f"🔗 尝试加载订阅: {url[:50]}...")
            
            # 多种获取方法
            methods = [
                lambda: self._fetch_with_httpx(url),
                lambda: self._fetch_with_curl(url),
                lambda: self._fetch_manual_list(url)
            ]
            
            for method in methods:
                try:
                    content = method()
                    if content:
                        proxies = self._parse_content(content)
                        if proxies:
                            print(f"✅ 从订阅获取到 {len(proxies)} 个代理")
                            return proxies
                except Exception as e:
                    print(f"⚠️ 获取方法失败: {e}")
                    continue
            
            return []
            
        except Exception as e:
            print(f"❌ 订阅加载失败: {e}")
            return []
    
    def _fetch_with_httpx(self, url: str) -> str:
        """使用httpx获取"""
        with httpx.Client(timeout=30, verify=False) as client:
            response = client.get(url)
            return response.text if response.status_code == 200 else ""
    
    def _fetch_with_curl(self, url: str) -> str:
        """使用curl命令获取（如果可用）"""
        import subprocess
        try:
            result = subprocess.run([
                'curl', '-k', '-s', '--max-time', '30', url
            ], capture_output=True, text=True, timeout=35)
            return result.stdout if result.returncode == 0 else ""
        except:
            return ""
    
    def _fetch_manual_list(self, url: str) -> str:
        """手动提供代理列表（备用方案）"""
        # 如果订阅链接失败，可以手动提供一些已知的代理
        manual_proxies = [
            "104.21.5.237:443",
            "172.67.154.194:443", 
            "104.18.219.221:443",
            "104.17.193.158:443",
            "*************:443"
        ]
        
        print("⚠️ 使用手动代理列表作为备用")
        return '\n'.join(manual_proxies)
    
    def _parse_content(self, content: str) -> List[str]:
        """解析内容提取HTTP代理"""
        proxies = []
        
        # 尝试Base64解码
        try:
            import base64
            decoded = base64.b64decode(content).decode('utf-8')
            content = decoded
        except:
            pass
        
        lines = content.strip().split('\n')
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            # 提取IP:端口格式的代理
            if ':' in line and not line.startswith('http'):
                # 简单的IP:端口格式
                parts = line.split(':')
                if len(parts) >= 2:
                    try:
                        host = parts[0].strip()
                        port = int(parts[1].strip())
                        if self._is_valid_proxy(host, port):
                            proxies.append(f"{host}:{port}")
                    except:
                        continue
            
            # 解析vless://或trojan://格式
            elif line.startswith(('vless://', 'trojan://')):
                proxy = self._extract_proxy_from_url(line)
                if proxy:
                    proxies.append(proxy)
        
        return list(set(proxies))  # 去重
    
    def _is_valid_proxy(self, host: str, port: int) -> bool:
        """验证代理格式"""
        import re
        # 简单的IP格式验证
        ip_pattern = r'^(\d{1,3}\.){3}\d{1,3}$'
        domain_pattern = r'^[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        
        return (re.match(ip_pattern, host) or re.match(domain_pattern, host)) and 1 <= port <= 65535
    
    def _extract_proxy_from_url(self, url: str) -> Optional[str]:
        """从vless://或trojan://URL提取代理"""
        try:
            if url.startswith('vless://'):
                config = url[8:]
            elif url.startswith('trojan://'):
                config = url[9:]
            else:
                return None
            
            # 提取@后面的服务器信息
            if '@' in config:
                server_part = config.split('@', 1)[1]
                
                # 提取主机和端口
                if '?' in server_part:
                    server_addr = server_part.split('?', 1)[0]
                else:
                    server_addr = server_part.split('#', 1)[0]
                
                if ':' in server_addr:
                    host, port = server_addr.rsplit(':', 1)
                    port = int(port)
                    
                    # 只返回443端口的代理（通常支持HTTP）
                    if port == 443:
                        return f"{host}:{port}"
            
            return None
            
        except Exception:
            return None
    
    def load_all_proxies(self) -> bool:
        """加载所有订阅的代理"""
        all_proxies = []
        
        for url in self.subscription_urls:
            proxies = self.load_proxies_from_subscription(url)
            all_proxies.extend(proxies)
        
        # 去重并随机排序
        self.proxies = list(set(all_proxies))
        random.shuffle(self.proxies)
        
        if self.proxies:
            print(f"✅ 总共加载 {len(self.proxies)} 个唯一代理")
            return True
        else:
            print("❌ 没有加载到任何代理")
            return False
    
    def get_current_proxy(self) -> Optional[str]:
        """获取当前代理"""
        if not self.proxies:
            return None
        
        if self.current_index >= len(self.proxies):
            self.current_index = 0
        
        return self.proxies[self.current_index]
    
    def switch_proxy(self) -> Optional[str]:
        """切换到下一个代理"""
        if not self.proxies:
            return None
        
        # 标记当前代理为失败
        current = self.get_current_proxy()
        if current:
            self.failed_proxies.add(current)
        
        # 寻找下一个可用代理
        attempts = 0
        while attempts < len(self.proxies):
            self.current_index = (self.current_index + 1) % len(self.proxies)
            next_proxy = self.get_current_proxy()
            
            if next_proxy and next_proxy not in self.failed_proxies:
                print(f"🔄 切换代理: {current} → {next_proxy}")
                return next_proxy
            
            attempts += 1
        
        print("❌ 没有更多可用代理")
        return None
    
    def test_proxy(self, proxy: str) -> bool:
        """测试代理连通性"""
        try:
            with httpx.Client(
                proxy=f"http://{proxy}",
                timeout=10
            ) as client:
                response = client.get("https://httpbin.org/ip")
                return response.status_code == 200
        except:
            return False


class SambaNovaWithProxy:
    """集成代理的SambaNova注册器示例"""
    
    def __init__(self, subscription_urls: List[str]):
        self.proxy_rotator = ProxyRotator(subscription_urls)
        self.client = None
        self.current_proxy = None
        
    def initialize_proxy(self) -> bool:
        """初始化代理"""
        print("🌐 初始化代理系统...")
        
        if not self.proxy_rotator.load_all_proxies():
            print("⚠️ 代理加载失败，将使用直连")
            self._create_direct_client()
            return False
        
        # 获取第一个代理
        proxy = self.proxy_rotator.get_current_proxy()
        if proxy:
            self._create_proxy_client(proxy)
            return True
        
        return False
    
    def _create_direct_client(self):
        """创建直连客户端"""
        self.client = httpx.Client(
            timeout=60.0,
            headers={'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'}
        )
        self.current_proxy = None
        print("🔗 使用直连模式")
    
    def _create_proxy_client(self, proxy: str):
        """创建代理客户端"""
        if self.client:
            self.client.close()
        
        self.client = httpx.Client(
            timeout=60.0,
            proxy=f"http://{proxy}",
            headers={'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'}
        )
        self.current_proxy = proxy
        print(f"🌐 使用代理: {proxy}")
    
    def handle_429_error(self) -> bool:
        """处理429错误 - 切换代理"""
        print("⚠️ 遇到429错误，尝试切换代理...")
        
        next_proxy = self.proxy_rotator.switch_proxy()
        if next_proxy:
            self._create_proxy_client(next_proxy)
            return True
        else:
            print("⚠️ 没有更多代理，切换到直连")
            self._create_direct_client()
            return False
    
    def register_with_retry(self) -> bool:
        """带重试的注册方法"""
        max_retries = 3
        
        for attempt in range(max_retries):
            try:
                # 模拟注册请求
                response = self.client.post(
                    "https://auth0.sambanova.ai/dbconnections/signup",
                    json={"test": "data"}
                )
                
                if response.status_code == 200:
                    print("✅ 注册成功")
                    return True
                elif response.status_code == 429:
                    print(f"⚠️ 429错误 (第 {attempt + 1} 次)")
                    if attempt < max_retries - 1:
                        if self.handle_429_error():
                            time.sleep(5)
                            continue
                        else:
                            time.sleep(10)
                            continue
                else:
                    print(f"❌ 注册失败: {response.status_code}")
                    return False
                    
            except Exception as e:
                print(f"❌ 请求异常: {e}")
                if attempt < max_retries - 1:
                    self.handle_429_error()
                    time.sleep(5)
                    continue
                
        return False


def main():
    """演示代理集成"""
    print("🌐 机场代理集成演示")
    print("=" * 50)
    
    # 订阅链接列表
    subscription_urls = [
        "https://52pokemon.xz61.cn/api/v1/client/subscribe?token=f8aaf630e7f05242986d7382f8dd57de",
        # 可以添加更多订阅链接作为备用
    ]
    
    # 创建集成代理的注册器
    registrar = SambaNovaWithProxy(subscription_urls)
    
    # 初始化代理
    if registrar.initialize_proxy():
        print("✅ 代理系统初始化成功")
        
        # 演示代理切换
        print("\n🔄 演示代理切换:")
        for i in range(3):
            current = registrar.current_proxy
            registrar.handle_429_error()
            new_proxy = registrar.current_proxy
            print(f"  第 {i+1} 次: {current} → {new_proxy}")
    
    else:
        print("⚠️ 代理系统初始化失败，使用直连模式")


if __name__ == "__main__":
    main()
